# -*- coding: utf-8 -*-
"""TakingAI.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1jWw4_jh-BLSytfnp54L1BFSYd43ijOw_
"""

!pip install datasets pandas pymongo sentence_transformers nltk
!pip install -U transformers torchvision pillow
# Install below if using GPU
!pip install accelerate

# 1️⃣ lấy và lưu dữ liệu
! pip install googletrans==4.0.0-rc1
! pip install eng-to-ipa
! pip install pymongo
! pip install requests passlib[bcrypt]

import pymongo
from google.colab import userdata
from flask import jsonify, request
from passlib.hash import bcrypt
from bson.objectid import ObjectId
from googletrans import Translator
import eng_to_ipa as ipa
import os

# 4️⃣install object detection
from transformers import DetrImageProcessor, DetrForObjectDetection
import torch
from PIL import Image, ImageDraw, ImageFont
import io
import base64

import threading
from flask import Flask, request, jsonify, send_file, send_from_directory
from pyngrok import ngrok
from flask_cors import CORS  # Import Flask-CORS
from kokoro import KPipeline
import soundfile as sf
import io
import requests
import json
import os
import uuid

# 2️⃣ install speech to text
!apt-get install -y ffmpeg
# Import các thư viện cần thiết
from flask import Flask, request, jsonify
from transformers import WhisperProcessor, WhisperForConditionalGeneration
import soundfile as sf
import subprocess
import tempfile
import shutil  # để kiểm tra ffmpeg

# 5️⃣install related word
import nltk
from nltk.corpus import wordnet
from nltk.collocations import BigramCollocationFinder
from nltk.metrics import BigramAssocMeasures

def get_mongo_client(mongo_uri):
  """Establish connection to the MongoDB."""
  try:
    client = pymongo.MongoClient(mongo_uri, appname="devrel.content.python")
    print("Connection to MongoDB successful")
    return client
  except pymongo.errors.ConnectionFailure as e:
    print(f"Connection failed: (e)")
    return None
mongo_uri = "mongodb+srv://vien10022003:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"

if not mongo_uri:
  print("MONGO_URI not set in environment variables")
mongo_client = get_mongo_client(mongo_uri)

db = mongo_client['english_app']
users_col = db['users']
vocab_col = db['vocabularies']

def validate_user_input(data):
    username = data.get("username")
    password = data.get("password")

    if not username or not password:
        return False, {"error": "Username and password are required"}, 400
    if len(username) < 3:
        return False, {"error": "Username must be at least 3 characters"}, 400
    if len(password) < 6:
        return False, {"error": "Password must be at least 6 characters"}, 400
    return True, username, password

def register_user(data, users_col):
    valid, result, code = validate_user_input(data)
    if not valid:
        return jsonify(result), code
    username, password = result, code

    if users_col.find_one({"username": username}):
        return jsonify({"error": "Username already exists"}), 400

    hashed_pw = bcrypt.hash(password)
    result = users_col.insert_one({"username": username, "password": hashed_pw})
    return jsonify({"message": "User registered", "user_id": str(result.inserted_id)}), 200


def login_user(data, users_col):
    valid, result, code = validate_user_input(data)
    if not valid:
        return jsonify(result), code
    username, password = result, code

    user = users_col.find_one({"username": username})
    if not user or not bcrypt.verify(password, user["password"]):
        return jsonify({"error": "Invalid username or password"}), 401

    return jsonify({"message": "Login successful", "user_id": str(user["_id"])})


translator = Translator()

def validate_vocab_input(data):
    user_id = data.get("user_id")
    word = data.get("word")

    if not user_id or not word:
        return False, {"error": "user_id and word are required"}, 400
    if len(word.strip()) == 0:
        return False, {"error": "Word cannot be empty"}, 400
    return True, user_id, word.strip(), data.get("example", "")

def enrich_word(word):
    try:
        # Dịch nghĩa tiếng Việt
        translation = translator.translate(word, src='en', dest='vi')
        meaning = translation.text
    except Exception:
        meaning = "(Không dịch được)"

    try:
        # Phiên âm IPA
        pronunciation = ipa.convert(word)
    except Exception:
        pronunciation = "(Không có phiên âm)"

    return meaning, pronunciation

def add_vocab_entry(data, users_col, vocab_col):

    valid, *rest = validate_vocab_input(data)
    if not valid:
        error_response, status_code = rest
        return jsonify(error_response), status_code  # Trả dict + status (route sẽ jsonify)

    user_id, word, example = rest

    # Kiểm tra user
    if not users_col.find_one({"_id": ObjectId(user_id)}):
        return jsonify({"error": "User not found"}), 404

    # Bước 3: Kiểm tra từ này đã tồn tại với người dùng chưa
    existing = vocab_col.find_one({
        "user_id": user_id,
        "word": word
    })
    if existing:
        return jsonify({
            "message": "Word already exists in user's vocabulary",
            "vocab_id": str(existing["_id"])
        }), 200  # OK - không thêm nữa

    # Xử lý dữ liệu bổ sung
    meaning_vi, ipa_text = enrich_word(word)

    # Lưu vào DB
    vocab_data = {
        "user_id": user_id,
        "word": word,
        "meaning": meaning_vi,
        "ipa": ipa_text,
        "example": example
    }

    result = vocab_col.insert_one(vocab_data)
    return jsonify({"message": "Vocabulary added", "vocab_id": str(result.inserted_id)}), 200

def del_vocab_entry(data, users_col, vocab_col):
    user_id = data.get("user_id")
    word = data.get("word")

    # Kiểm tra dữ liệu
    if not user_id or not word:
        return jsonify({"error": "Missing 'user_id' or 'word' in request"}), 400

    # Kiểm tra user tồn tại
    if not users_col.find_one({"_id": ObjectId(user_id)}):
        return jsonify({"error": "User not found"}), 404

    # Tìm và xóa từ vựng
    result = vocab_col.delete_one({
        "user_id": user_id,
        "word": word
    })

    if result.deleted_count == 0:
        return jsonify({"message": "Word not found in user's vocabulary"}), 404

    return jsonify({"message": "Vocabulary entry deleted"}), 200


def get_paginated_vocabs(user_id, vocab_col):
    try:
        page_size = int(request.args.get("pageSize", 10))
        page_index = int(request.args.get("pageIndex", 0))
        assert page_size > 0 and page_index >= 0
    except (ValueError, AssertionError):
        return jsonify({"error": "Invalid pageSize or pageIndex"}), 400

    # Đếm tổng số từ vựng của user
    total_count = vocab_col.count_documents({"user_id": user_id})
    total_pages = (total_count + page_size - 1) // page_size

    # Truy vấn dữ liệu theo phân trang
    cursor = vocab_col.find({"user_id": user_id}) \
                      .skip(page_index * page_size) \
                      .limit(page_size)

    vocabs = list(cursor)
    for v in vocabs:
        v["_id"] = str(v["_id"])

    return jsonify({
        "data": vocabs,
        "totalCount": total_count,
        "totalPages": total_pages,
        "pageIndex": page_index,
        "pageSize": page_size
    })

def generate_speech(text, voice='af_heart', speed=1):
    """
    Sinh file âm thanh từ văn bản đầu vào, trả về buffer WAV.
    """
    generator = pipeline(
        text,
        voice=voice,
        speed=speed,
        split_pattern=r'\n+'
    )

    audio_buffer = io.BytesIO()
    all_audio = []

    for _, _, audio in generator:
        all_audio.extend(audio)

    sf.write(audio_buffer, all_audio, 24000, format='WAV')

    display(Audio(data=all_audio, rate=24000, autoplay=False))
    audio_buffer.seek(0)
    return audio_buffer


# Tải mô hình và processor từ Hugging Face
processorSpeechText = WhisperProcessor.from_pretrained("openai/whisper-base.en")
modelSpeechText = WhisperForConditionalGeneration.from_pretrained("openai/whisper-base.en")

def transcribe_audio_file(audio_file):
    """Xử lý file âm thanh và trả về nội dung văn bản."""
    try:
        # Kiểm tra xem ffmpeg có tồn tại không
        if not shutil.which("ffmpeg"):
            return "FFmpeg chưa được cài đặt hoặc không có trong PATH."

        # Lưu file đầu vào tạm thời
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_input:
            audio_file.save(temp_input.name)
            input_path = temp_input.name

        # Tạo file đầu ra sau khi chuyển tần số
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_output:
            output_path = temp_output.name

        # Gọi FFmpeg để chuyển thành 16 kHz, mono, PCM 16-bit
        subprocess.run([
            "ffmpeg", "-y", "-i", input_path,
            "-ar", "16000", "-ac", "1",
            "-f", "wav", "-acodec", "pcm_s16le",
            output_path
        ], check=True)

        # Đọc file đã chuẩn hóa
        audio_data, sample_rate = sf.read(output_path)

        # Tiền xử lý và chuẩn bị input cho model
        input_features = processorSpeechText(audio_data, sampling_rate=sample_rate, return_tensors="pt").input_features

        # Dự đoán và giải mã
        predicted_ids = modelSpeechText.generate(input_features)
        transcription = processorSpeechText.batch_decode(predicted_ids, skip_special_tokens=True)[0]

        return transcription

    except Exception as e:
        return f"Lỗi khi xử lý âm thanh: {str(e)}"

from huggingface_hub import login
login()

# 3️⃣ install text generation for answer the question
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# tokenizer = AutoTokenizer.from_pretrained("TinyLlama/TinyLlama-1.1B-Chat-v1.0")
# #GPU Enabled use below
# modelTextGen = AutoModelForCausalLM.from_pretrained("TinyLlama/TinyLlama-1.1B-Chat-v1.0", device_map="auto")

tokenizer = AutoTokenizer.from_pretrained("arcee-ai/Arcee-VyLinh")
#GPU Enabled use below
modelTextGen = AutoModelForCausalLM.from_pretrained("arcee-ai/Arcee-VyLinh", device_map="auto")

def handle_query(query, tokenizer, modelTextGen):
    """
    Hàm nhận vào query, tìm kiếm thông tin từ một nguồn, và trả về câu trả lời.

    Args:
        query (str): Truy vấn từ người dùng.
        tokenizer: Tokenizer dùng để xử lý dữ liệu và tạo input cho modelTextGen.
        model: Mô hình sinh text (e.g., GPT, transformer modelTextGen).

    Returns:
        str: Câu trả lời được sinh ra từ modelTextGen.
    """
    # Tìm kiếm thông tin liên quan đến truy vấn
    combined_information = f"{query}"
    # Tạo nội dung tin nhắn
    messages = [
        {
            "role": "system",
            "content": (
                "You are Emma, an extremely friendly, bubbly, and energetic AI assistant. "
                "You speak like a cheerful best friend who is always supportive, playful, and full of life. "
                "You make conversations light, engaging, and encouraging. "
                "You never judge, you're curious, fun-loving, and speak with warmth and enthusiasm. "
                "You're also smart and helpful, and you explain things clearly with a positive vibe! "
                "Do not use any emojis in your responses."
                "Keep your answers concise and to the point, unless the user asks for more details."
            ),
        },
        {
            "role": "user",
            "content": combined_information
        }
    ]



    # Chuẩn bị dữ liệu input cho mô hình
    text = tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True
    )
    model_inputs = tokenizer([text], return_tensors="pt").to('cuda')

    # Sinh đầu ra từ modelTextGen
    generated_ids = modelTextGen.generate(
        model_inputs.input_ids,
        max_new_tokens=1024,
        eos_token_id=tokenizer.eos_token_id,
        temperature=0.25,
    )
    generated_ids = [
        output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
    ]

    # Giải mã kết quả và trả về
    response = tokenizer.batch_decode(generated_ids)[0]
    # Xóa các token không mong muốn như "<|im_end|>"
    response = response.replace("<|im_end|>", "").strip()
    return response

# Ví dụ sử dụng hàm

# Khởi tạo mô hình và processor của DETR
processorDetect = DetrImageProcessor.from_pretrained("facebook/detr-resnet-50", revision="no_timm")
modelDetect = DetrForObjectDetection.from_pretrained("facebook/detr-resnet-50", revision="no_timm")

# Hàm để giải mã ảnh từ base64
def decode_base64_image(base64_string):
    header, encoded = base64_string.split(",", 1)
    image_data = base64.b64decode(encoded)
    image = Image.open(io.BytesIO(image_data)).convert("RGB")
    return image

def mark_objects_on_image(image, results):
    draw = ImageDraw.Draw(image)

    # Font to use for labels (tăng kích thước lên 20)
    try:
        font = ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf", 16)
    except:
        print("2...........ImageFont.load_default()")
        font = ImageFont.load_default()

    for _, label, box in zip(results["scores"], results["labels"], results["boxes"]):
        xmin, ymin, xmax, ymax = box.tolist()
        draw.rectangle([xmin, ymin, xmax, ymax], outline="red", width=3)
        draw.text((xmin, ymin), f"{modelDetect.config.id2label[label.item()]}", fill="yellow", font=font)

    return image

def process_image(image):
    inputs = processorDetect(images=image, return_tensors="pt")
    outputs = modelDetect(**inputs)

    target_sizes = torch.tensor([image.size[::-1]])
    results = processorDetect.post_process_object_detection(
        outputs, target_sizes=target_sizes, threshold=0.9
    )[0]

    detected_objects = []
    seen_words = set()  # để ghi nhớ từ đã xử lý

    for score, label, box in zip(results["scores"], results["labels"], results["boxes"]):
        word = modelDetect.config.id2label[label.item()]
        if word in seen_words:
            continue  # bỏ qua nếu từ đã có rồi

        seen_words.add(word)
        box = [round(i, 2) for i in box.tolist()]
        enriched = enrich_word_data(word)
        detected_objects.append({
            **enriched,
            "score": round(score.item(), 3),
            "box": box
        })

    marked_image = mark_objects_on_image(image, results)
    return marked_image, detected_objects

# Tải dữ liệu NLTK nếu chưa có
nltk.download('wordnet')
nltk.download('brown')
nltk.download('punkt')



def enrich_word_data(word):
    """Dịch sang tiếng Việt và lấy phiên âm IPA cho một từ."""
    # Dịch
    try:
        translation = translator.translate(word, src='en', dest='vi')
        meaning = translation.text
    except Exception:
        meaning = "(Không dịch được)"
    # Phiên âm
    try:
        pronunciation = ipa.convert(word)
    except Exception:
        pronunciation = "(Không có phiên âm)"
    return {"word": word, "meaning": meaning, "ipa": pronunciation}

def get_meaning(word):
    """Dịch sang tiếng Việt"""
    # Dịch
    try:
        translation = translator.translate(word, src='en', dest='vi')
        meaning = translation.text
    except Exception:
        meaning = "(Không dịch được)"
    return meaning

def get_ipa(word):
    """lấy phiên âm IPA cho một từ."""
    try:
        pronunciation = ipa.convert(word)
    except Exception:
        pronunciation = "(Không có phiên âm)"
    return pronunciation

# 1. Từ đồng nghĩa
def get_synonyms(word, limit=10):
    """Lấy từ đồng nghĩa từ 7 nghĩa đầu tiên và in tổng số nghĩa."""
    word_lower = word.lower()
    synsets = wordnet.synsets(word)

    if not synsets:
        return [{"word": word, "meaning": "(Không tìm thấy từ)", "ipa": "(Không có phiên âm)"}]

    # In tổng số nghĩa
    print(f"Tổng số nghĩa của '{word}': {len(synsets)}")

    # Lấy tối đa 7 synsets đầu tiên
    selected_synsets = synsets[:7]
    print(f"Số nghĩa được chọn: {len(selected_synsets)}")

    # In định nghĩa của các synsets được chọn
    print("Các nghĩa được chọn:")
    for i, syn in enumerate(selected_synsets, 1):
        print(f"Nghĩa {i}: {syn.definition()}")

    # Lấy từ đồng nghĩa từ các synsets được chọn
    syns = set()
    for syn in selected_synsets:
        for lemma in syn.lemmas():
            name = lemma.name().replace('_', ' ').strip().lower()
            if name != word_lower:
                syns.add(name)

    # Giới hạn và làm giàu dữ liệu
    result = []
    for w in list(syns)[:limit]:
        result.append(enrich_word_data(w))
    return result



# # 2. Từ liên tưởng (Datamuse API)
# def get_related_words(word, limit=10):
#     """Lấy từ liên tưởng qua Datamuse API."""
#     try:
#         resp = requests.get(f"https://api.datamuse.com/words?ml={word}&max={limit}")
#         words = [item['word'] for item in resp.json()] if resp.status_code == 200 else []
#     except Exception:
#         words = []
#     result = [enrich_word_data(w) for w in words]
#     return result

# # 3. Từ gần nghĩa (rộng hơn)
# def get_semantic_field(word, limit=10):
#     """Lấy từ trong cùng trường ngữ nghĩa (hypernyms + hyponyms)."""
#     fields = set()
#     for syn in wordnet.synsets(word):
#         fields.update(h.name().split('.')[0].replace('_', ' ') for h in syn.hypernyms())
#         fields.update(h.name().split('.')[0].replace('_', ' ') for h in syn.hyponyms())
#     result = []
#     for w in list(fields)[:limit]:
#         result.append(enrich_word_data(w))
#     return result


# 5. Từ trái nghĩa
def get_antonyms(word, limit=10):
    """Lấy từ trái nghĩa."""
    word_lower = word.lower()
    ants = set()
    for syn in wordnet.synsets(word):
        for lemma in syn.lemmas():
            for ant in lemma.antonyms():
                name = ant.name().replace('_', ' ').strip().lower()
                if name != word_lower:
                    ants.add(name)
    result = []
    for w in list(ants)[:limit]:
        result.append(enrich_word_data(w))
    return result

# 6. Cụm từ
API_KEY = "52b8sg5nqf1fix84vpzb6g4huglgtec5fsbiimqnjwpz7l3uk"

def get_phrases(word, limit=5):
    """Lấy các cụm từ phổ biến chứa từ và enrich từng cụm."""
    url = f"https://api.wordnik.com/v4/word.json/{word}/phrases"
    params = {
        "limit": limit,
        "api_key": API_KEY
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        phrases_raw = [
            f"{item['gram1']} {item['gram2']}".strip()
            for item in data
        ]

        # Dùng dict để loại trùng theo lowercase nhưng vẫn giữ cụm gốc
        unique_phrases = list({phrase.lower(): phrase for phrase in phrases_raw}.values())[:limit]

        # Enrich từng cụm từ
        phrases = [enrich_word_data(phrase) for phrase in unique_phrases]

        return phrases

    except Exception as e:
        print(f"Error fetching phrases: {e}")
        return []

# 6️⃣ Install kokoro for text to speech
!pip install -q kokoro>=0.9.4 soundfile
# Install espeak, used for English OOD fallback and some non-English languages
!apt-get -qq -y install espeak-ng > /dev/null 2>&1

# Initalize a pipeline
from kokoro import KPipeline
from IPython.display import display, Audio
import soundfile as sf
import torch
# 🇺🇸 'a' => American English, 🇬🇧 'b' => British English
# 🇪🇸 'e' => Spanish es
# 🇫🇷 'f' => French fr-fr
# 🇮🇳 'h' => Hindi hi
# 🇮🇹 'i' => Italian it
# 🇯🇵 'j' => Japanese: pip install misaki[ja]
# 🇧🇷 'p' => Brazilian Portuguese pt-br
# 🇨🇳 'z' => Mandarin Chinese: pip install misaki[zh]
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Device:", device)
pipeline = KPipeline(lang_code='a',device=device) # <= make sure lang_code matches voice, reference above.

# This text is for demonstration purposes only, unseen during training
# text = '''
# The sky above the port was the color of television, tuned to a dead channel.
# "It's not like I'm using," Case heard someone say, as he shouldered his way through the crowd around the door of the Chat. "It's like my body's developed this massive drug deficiency."
# It was a Sprawl voice and a Sprawl joke. The Chatsubo was a bar for professional expatriates; you could drink there for a week and never hear two words in Japanese.
# '''
text=""
# Generate, display, and save audio files in a loop.
generator = pipeline(
    text, voice='af_heart', # <= change voice here
    speed=1, split_pattern=r'\n+'
)

for i, (gs, ps, audio) in enumerate(generator):
    print(i)  # i => index
    print(gs) # gs => graphemes/text
    print(ps) # ps => phonemes
    display(Audio(data=audio, rate=24000, autoplay=i==0))
    sf.write(f'{i}.wav', audio, 24000) # save each audio file

# xuat api
! pip install pyngrok
! pip install flask-ngrok
! pip install flask-cors

! ngrok authtoken '*************************************************'

# Khởi tạo ứng dụng Flask
app = Flask(__name__)
CORS(app)  # Cho phép tất cả các nguồn truy cập API

@app.route('/gen-text', methods=['POST'])
def api_query():
    # Nhận JSON từ yêu cầu POST
    data = request.get_json()
    if 'query' not in data:
        return jsonify({"error": "Missing 'query' in request"}), 400

    query = data['query']
    # Gọi hàm handle_query để xử lý
    response = handle_query(query, tokenizer, modelTextGen)
    print("---response: ", response)
    return jsonify({"response": response})

pipeline = KPipeline(lang_code='a')  # American English
@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    data = request.get_json()
    if not data or 'query' not in data:
        return jsonify({"error": "Missing 'query' in request"}), 400

    text = data['query']
    voice = data.get('voice', 'af_heart')  # dùng mặc định nếu thiếu
    speed = float(data.get('speed', 1.0))  # chuyển sang float

    try:
        audio_buffer = generate_speech(text, voice=voice, speed=speed)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

    return send_file(
        audio_buffer,
        mimetype='audio/wav',
        as_attachment=True,
        download_name='output.wav'
    )

@app.route('/speech-to-text', methods=['POST'])
def speech_to_text():
    if 'audio' not in request.files:
        return jsonify({"error": "Missing audio file"}), 400

    audio_file = request.files['audio']
    result = transcribe_audio_file(audio_file)

    if result.startswith("Error") or "Traceback" in result:
        return jsonify({"error": result}), 500

    return jsonify({"transcription": result})


@app.route('/detect-object', methods=['POST'])
def detect_object():
    file = request.files['image']
    image = Image.open(file.stream).convert("RGB")

    marked_image, detected_objects = process_image(image)

    # Tạo file ảnh đã đánh dấu và lưu vào thư mục static
    image_id = str(uuid.uuid4())
    filename = f'marked_image_{image_id}.jpg'
    filepath = os.path.join('static/results', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    marked_image.save(filepath, format='JPEG')

    # Tạo URL để client truy cập ảnh thông qua API get-image
    image_url = ngrok_url + f'/get-image/{filename}'
    # image_url = request.host_url + f'static/results/{filename}'

    return jsonify({
        "image_url": image_url,
        "objects": detected_objects
    })

@app.route('/get-image/<filename>')
def get_image(filename):
    """Trả về ảnh đã được đánh dấu dựa trên tên file."""
    directory = os.path.join('static', 'results')
    return send_from_directory(directory, filename)


from flask import Flask, request, jsonify
from concurrent.futures import ThreadPoolExecutor

@app.route("/related-word", methods=["POST"])
def related_words():
    data = request.get_json()
    word = data.get("word", "").strip().lower()
    limit = int(data.get("limit", 10))

    if not word:
        return jsonify({"error": "Missing 'word' in request body"}), 400

    # Dùng ThreadPoolExecutor để gọi các hàm song song
    with ThreadPoolExecutor() as executor:
        futures = {
            "ipa": executor.submit(get_ipa, word),
            "meaning": executor.submit(get_meaning, word),
            "synonyms": executor.submit(get_synonyms, word, limit),
            "phrases": executor.submit(get_phrases, word, limit),
            "antonyms": executor.submit(get_antonyms, word, limit),
        }

        # Lấy kết quả khi xong
        result = {
            "word": word,
            "limit": limit
        }

        for key, future in futures.items():
            try:
                result[key] = future.result()
            except Exception as e:
                result[key] = None
                print(f"Error in {key}: {e}")

    return jsonify(result)


@app.route("/users/register", methods=["POST"])
def register():
    return register_user(request.json, users_col)

@app.route("/users/login", methods=["POST"])
def login():
    return login_user(request.json, users_col)

@app.route("/vocab", methods=["POST"])
def add_vocab():
    return add_vocab_entry(request.json, users_col, vocab_col)

@app.route("/vocab", methods=["DELETE"])
def delete_vocab():
    return del_vocab_entry(request.json, users_col, vocab_col)

@app.route("/vocab/<user_id>", methods=["GET"])
def get_vocab(user_id):
    return get_paginated_vocabs(user_id, vocab_col)

# Cài đặt Ngrok và mở tunnel cho Flask app
public_url = ngrok.connect(5000)
print(f"Ngrok tunnel URL: {public_url}")

# Đẩy url lên firebase realtime database
url = "https://vienvipvail-default-rtdb.firebaseio.com/api-android-ngrok.json"
ngrok_url = public_url.public_url
response = requests.put(url, data=json.dumps(ngrok_url))

# Chạy Flask app trong một luồng mới để không bị chặn trong Colab
# def run_app():
#     app.run(port=5000)
# thread = threading.Thread(target=run_app)
# thread.start()

if __name__ == "__main__":
    app.run(port=5000)

import threading

for thread in threading.enumerate():
    print(thread.name, thread.is_alive())