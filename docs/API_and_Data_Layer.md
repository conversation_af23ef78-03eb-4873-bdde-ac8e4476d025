# 🌐 LingoAI - API và Data Layer Documentation

## 🏗️ Data Architecture Overview

LingoAI sử dụng **Repository Pattern** với **Clean Architecture** để tách biệt data layer khỏi UI layer, đảm bảo testability và maintainability.

```mermaid
graph TD
    A[UI Layer] --> B[ViewModel]
    B --> C[Repository]
    C --> D[Data Sources]
    D --> E[Remote API]
    D --> F[Local DataStore]
    D --> G[Assets]
```

## 🌐 API Service Layer

### 📡 ApiService.kt - Centralized API Management

```kotlin
object ApiService {
    private var BASE_URL = "https://vienvipvail-default-rtdb.firebaseio.com"
    private var USER_ID = "abcb"
    private val client = OkHttpClient()
    
    // Dynamic base URL từ Firebase
    fun fetchAndSetBaseUrl(onComplete: ((success: Boolean) -> Unit)? = null)
    
    // Authentication APIs
    fun loginUser(username: String, password: String, callback: (Int, String?) -> Unit)
    fun registerUser(username: String, password: String, callback: (Int, String?) -> Unit)
    
    // Vocabulary APIs
    fun searchVocabulary(word: String, callback: (Int, String?) -> Unit)
    fun addVocabulary(word: String, meaning: String?, callback: (Int, String?) -> Unit)
    fun getVocabularyList(pageSize: Int, pageIndex: Int, callback: (Int, String?) -> Unit)
    fun deleteVocabulary(word: String, callback: (Int, String?) -> Unit)
    
    // AI Features APIs
    fun detectObject(imageFile: File, callback: (String?) -> Unit)
    fun generateText(prompt: String, callback: (Int, String?) -> Unit)
    
    // Utility
    fun setUserId(userId: String)
}
```

### 🔗 API Endpoints

#### 🔐 Authentication Endpoints
```
POST /users/login
POST /users/register
```

**Request Format:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response Format:**
```json
{
  "user_id": "string",
  "message": "string"
}
```

#### 📚 Vocabulary Endpoints
```
GET /vocabulary/search?word={word}&user_id={user_id}
POST /vocabulary/add
GET /vocabulary/list?user_id={user_id}&page_size={size}&page_index={index}
DELETE /vocabulary/delete?word={word}&user_id={user_id}
```

**Search Response:**
```json
{
  "word": "string",
  "ipa": "string",
  "meaning": "string",
  "synonyms": ["string"],
  "antonyms": ["string"],
  "related_words": ["string"],
  "audio_url": "string"
}
```

**List Response:**
```json
{
  "data": [
    {
      "word": "string",
      "ipa": "string", 
      "meaning": "string"
    }
  ],
  "total": "number",
  "page_size": "number",
  "page_index": "number"
}
```

#### 🤖 AI Feature Endpoints
```
POST /detect-object (multipart/form-data)
POST /generate-text
```

**Object Detection Response:**
```json
{
  "objects": [
    {
      "label": "string",
      "confidence": "number",
      "bbox": {
        "x": "number",
        "y": "number", 
        "width": "number",
        "height": "number"
      },
      "vietnamese_meaning": "string"
    }
  ]
}
```

**Text Generation Response:**
```json
{
  "response": "string",
  "confidence": "number"
}
```

### 🔧 API Implementation Details

#### Dynamic Base URL Management
```kotlin
fun fetchAndSetBaseUrl(onComplete: ((success: Boolean) -> Unit)? = null) {
    val request = Request.Builder()
        .url("https://vienvipvail-default-rtdb.firebaseio.com/api-android-ngrok.json")
        .get()
        .build()

    client.newCall(request).enqueue(object : Callback {
        override fun onResponse(call: Call, response: Response) {
            try {
                val responseBody = response.body?.string()
                if (response.isSuccessful && responseBody != null) {
                    BASE_URL = responseBody.trim('"')
                    Log.d("ApiService", "Updated BASE_URL to: $BASE_URL")
                    onComplete?.invoke(true)
                }
            } catch (e: Exception) {
                onComplete?.invoke(false)
            }
        }
        
        override fun onFailure(call: Call, e: IOException) {
            onComplete?.invoke(false)
        }
    })
}
```

#### Error Handling Strategy
```kotlin
private fun handleApiResponse(
    call: Call, 
    response: Response, 
    callback: (Int, String?) -> Unit
) {
    try {
        val responseBody = response.body?.string()
        callback(response.code, responseBody)
    } catch (e: Exception) {
        Log.e("ApiService", "Error processing response: ${e.message}")
        callback(-1, e.message)
    }
}
```

## 💾 Data Models

### 👤 User Models
```kotlin
data class User(
    val id: String,
    val username: String,
    val email: String? = null,
    val createdAt: Long = System.currentTimeMillis()
)

data class LoginRequest(
    val username: String,
    val password: String
)

data class LoginResponse(
    val user_id: String,
    val message: String
)
```

### 📚 Vocabulary Models
```kotlin
data class VocabularyItem(
    val word: String,
    val ipa: String,
    val meaning: String,
    val synonyms: List<String> = emptyList(),
    val antonyms: List<String> = emptyList(),
    val relatedWords: List<String> = emptyList(),
    val audioUrl: String? = null,
    val createdAt: Long = System.currentTimeMillis()
)

data class HistoryItem(
    val word: String,
    val phonetic: String,
    val meaning: String
)
```

### 🃏 Flashcard Models
```kotlin
data class Flashcard(
    val id: String = UUID.randomUUID().toString(),
    val front: String,        // Từ tiếng Anh
    val back: String,         // Nghĩa tiếng Việt
    val ipa: String = "",     // Phiên âm IPA
    val setId: String,        // ID của bộ flashcard
    val createdAt: Long = System.currentTimeMillis(),
    var isLearned: Boolean = false
)

data class FlashcardSet(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val flashcards: List<Flashcard> = emptyList()
)

// Serializable versions cho DataStore
@Serializable
data class SerializableFlashcard(
    val id: String = "",
    val front: String,
    val back: String,
    val ipa: String = "",
    val setId: String,
    val createdAt: Long = System.currentTimeMillis(),
    val isLearned: Boolean = false
)

@Serializable
data class SerializableFlashcardSet(
    val id: String = "",
    val name: String,
    val description: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val flashcards: List<SerializableFlashcard> = emptyList()
)
```

### 💬 Chat Models
```kotlin
data class ChatMessage(
    val sender: String,
    val text: String,
    val isUser: Boolean,
    val timestamp: Long = System.currentTimeMillis()
)

enum class RecordingState {
    IDLE, RECORDING, PROCESSING, COMPLETED, ERROR
}
```

### 👁️ Vision Models
```kotlin
data class DetectedObject(
    val label: String,
    val confidence: Float,
    val bbox: BoundingBox,
    val vietnameseMeaning: String
)

data class BoundingBox(
    val x: Float,
    val y: Float,
    val width: Float,
    val height: Float
)

enum class VisionaryStep {
    Welcome, Camera, Result
}
```

## 🗄️ Repository Pattern Implementation

### 🃏 FlashcardRepository
```kotlin
class FlashcardRepository(
    private val dataStore: FlashcardDataStore,
    private val userPreferences: UserPreferences,
    private val scope: CoroutineScope
) {
    private val _flashcardSets = MutableStateFlow<List<FlashcardSet>>(emptyList())
    val flashcardSets: StateFlow<List<FlashcardSet>> = _flashcardSets.asStateFlow()
    
    private val _currentSet = MutableStateFlow<FlashcardSet?>(null)
    val currentSet: StateFlow<FlashcardSet?> = _currentSet.asStateFlow()
    
    // CRUD Operations
    fun addFlashcardSet(name: String, description: String)
    fun deleteFlashcardSet(setId: String)
    fun addFlashcard(setId: String, front: String, back: String, ipa: String)
    fun deleteFlashcard(setId: String, flashcardId: String)
    fun updateFlashcard(flashcard: Flashcard)
    fun addFlashcardsBatch(setId: String, cards: List<Triple<String, String, String>>)
    
    // State Management
    fun setCurrentSet(setId: String)
    fun clearCurrentSet()
    
    private fun saveData() {
        scope.launch {
            saveMutex.withLock {
                dataStore.saveFlashcardSets(_flashcardSets.value)
            }
        }
    }
}
```

### 💾 Data Storage Layer

#### 👤 UserPreferences (DataStore)
```kotlin
class UserPreferences(private val context: Context) {
    companion object {
        private val USER_ID_KEY = stringPreferencesKey("user_id")
        private val USERNAME_KEY = stringPreferencesKey("username")
        private val AUTH_TOKEN_KEY = stringPreferencesKey("auth_token")
    }
    
    // Save operations
    suspend fun saveUserData(userId: String, username: String)
    suspend fun saveAuthToken(token: String)
    suspend fun clearUserData()
    
    // Read operations
    val userId: Flow<String?> = context.dataStore.data.map { it[USER_ID_KEY] }
    val username: Flow<String?> = context.dataStore.data.map { it[USERNAME_KEY] }
    val authToken: Flow<String?> = context.dataStore.data.map { it[AUTH_TOKEN_KEY] }
    val isLoggedIn: Flow<Boolean> = userId.map { !it.isNullOrEmpty() }
}
```

#### 🃏 FlashcardDataStore
```kotlin
class FlashcardDataStore(
    private val context: Context,
    private val userPreferences: UserPreferences
) {
    // Per-user data isolation
    private fun getFlashcardSetsKey(): String {
        return "flashcard_sets_${userPreferences.userId.value ?: "default"}"
    }
    
    // Save flashcard sets
    suspend fun saveFlashcardSets(sets: List<FlashcardSet>) {
        try {
            val serializableSets = sets.map { convertToSerializable(it) }
            val jsonString = Json.encodeToString(serializableSets)
            val key = stringPreferencesKey(getFlashcardSetsKey())
            
            context.flashcardDataStore.edit { preferences ->
                preferences[key] = jsonString
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    // Load flashcard sets
    val flashcardSets: Flow<List<FlashcardSet>> = userPreferences.userId.map { userId ->
        try {
            val keyName = if (userId != null) "flashcard_sets_$userId" else "flashcard_sets"
            val key = stringPreferencesKey(keyName)
            
            val preferences = context.flashcardDataStore.data.first()
            val jsonString = preferences[key] ?: return@map emptyList()
            val serializableSets = Json.decodeFromString<List<SerializableFlashcardSet>>(jsonString)
            
            serializableSets.map { convertFromSerializable(it) }
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }
}
```

## 🔄 State Management

### 📊 ViewModel Pattern
```kotlin
class FlashcardViewModel(
    private val repository: FlashcardRepository
) : ViewModel() {
    
    // Expose repository states
    val flashcardSets = repository.flashcardSets
    val currentSet = repository.currentSet
    
    // UI states
    private val _importProgress = MutableStateFlow<ImportProgress>(ImportProgress.Idle)
    val importProgress: StateFlow<ImportProgress> = _importProgress.asStateFlow()
    
    // Business logic
    fun addFlashcardSet(name: String, description: String) {
        repository.addFlashcardSet(name, description)
    }
    
    fun importFromQuizlet(setId: String, url: String) {
        viewModelScope.launch {
            try {
                _importProgress.value = ImportProgress.Loading
                val importedCards = QuizletImporter.importFromUrl(url)
                
                importedCards.forEach { card ->
                    repository.addFlashcard(setId, card.front, card.back, card.ipa)
                }
                
                _importProgress.value = ImportProgress.Success(importedCards.size)
            } catch (e: Exception) {
                _importProgress.value = ImportProgress.Error(e.message ?: "Import failed")
            }
        }
    }
}

sealed class ImportProgress {
    object Idle : ImportProgress()
    object Loading : ImportProgress()
    data class Success(val count: Int) : ImportProgress()
    data class Error(val message: String) : ImportProgress()
}
```

### 🔄 Data Flow Examples

#### API Call Flow
```kotlin
// 1. UI triggers action
Button(onClick = { viewModel.searchWord(searchText) })

// 2. ViewModel processes
fun searchWord(word: String) {
    viewModelScope.launch {
        _isLoading.value = true
        
        ApiService.searchVocabulary(word) { code, response ->
            _isLoading.value = false
            
            if (code == 200 && response != null) {
                val vocabularyItem = parseVocabularyResponse(response)
                _searchResult.value = vocabularyItem
            } else {
                _error.value = "Search failed"
            }
        }
    }
}

// 3. UI reacts to state changes
val searchResult by viewModel.searchResult.collectAsState()
val isLoading by viewModel.isLoading.collectAsState()

if (isLoading) {
    CircularProgressIndicator()
} else {
    searchResult?.let { VocabularyDisplay(it) }
}
```

#### Local Data Flow
```kotlin
// 1. Repository observes DataStore
val flashcardSets: Flow<List<FlashcardSet>> = dataStore.flashcardSets

// 2. ViewModel exposes to UI
val flashcardSets = repository.flashcardSets.asStateFlow()

// 3. UI collects state
val sets by viewModel.flashcardSets.collectAsState()

LazyColumn {
    items(sets) { set ->
        FlashcardSetItem(set)
    }
}
```

## 🔧 Data Validation & Error Handling

### ✅ Input Validation
```kotlin
object ValidationUtils {
    fun validateUsername(username: String): ValidationResult {
        return when {
            username.isBlank() -> ValidationResult.Error("Username cannot be empty")
            username.length < 3 -> ValidationResult.Error("Username too short")
            !username.matches(Regex("^[a-zA-Z0-9_]+$")) -> 
                ValidationResult.Error("Invalid characters")
            else -> ValidationResult.Success
        }
    }
    
    fun validatePassword(password: String): ValidationResult {
        return when {
            password.length < 6 -> ValidationResult.Error("Password too short")
            else -> ValidationResult.Success
        }
    }
}

sealed class ValidationResult {
    object Success : ValidationResult()
    data class Error(val message: String) : ValidationResult()
}
```

### 🚨 Error Handling
```kotlin
// Network error handling
fun handleApiError(code: Int, message: String?): String {
    return when (code) {
        -1 -> "Kết nối mạng thất bại"
        -2 -> "Không có kết nối internet"
        400 -> "Dữ liệu không hợp lệ"
        401 -> "Phiên đăng nhập hết hạn"
        404 -> "Không tìm thấy dữ liệu"
        500 -> "Lỗi server, vui lòng thử lại"
        else -> message ?: "Lỗi không xác định"
    }
}

// Data persistence error handling
suspend fun safeDataOperation(operation: suspend () -> Unit) {
    try {
        operation()
    } catch (e: Exception) {
        Log.e("DataError", "Data operation failed: ${e.message}")
        // Fallback or retry logic
    }
}
```

---

*Data layer này đảm bảo data consistency, error resilience và optimal performance cho LingoAI.*
