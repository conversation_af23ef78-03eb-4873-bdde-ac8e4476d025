# 🔄 LingoAI - <PERSON><PERSON><PERSON>ử <PERSON>ý Chi Tiết

## 🚀 Application Lifecycle Flow

### 1. 🌟 App Startup Sequence

```mermaid
graph TD
    A[App Launch] --> B[SplashActivity]
    B --> C[MainActivity.onCreate]
    C --> D[Check Permissions]
    D --> E[Fetch Base URL from Firebase]
    E --> F[Check Login Status]
    F --> G{Is Logged In?}
    G -->|Yes| H[Set UserId in ApiService]
    G -->|No| I[Navigate to Login]
    H --> J[Navigate to Home]
    I --> K[Login/Register Flow]
    K --> J
```

**Chi tiết từng bước:**

#### 🎬 SplashActivity
```kotlin
// Hiển thị logo LingoAI
// Thời gian hiển thị: 2-3 giây
// Tự động chuyển sang MainActivity
```

#### 📱 MainActivity.onCreate()
```kotlin
override fun onCreate(savedInstanceState: Bundle?) {
    // 1. <PERSON><PERSON><PERSON> tra permissions
    if (!allPermissionsGranted()) {
        requestPermissions()
    }
    
    // 2. Fetch base URL từ Firebase
    ApiService.fetchAndSetBaseUrl { success ->
        if (success) {
            // 3. Kiểm tra login status
            checkLoginStatus()
        }
    }
}
```

#### 🔐 Authentication Check
```kotlin
lifecycleScope.launch {
    val userPreferences = UserPreferences(applicationContext)
    isUserLoggedIn = userPreferences.isLoggedIn.first()
    val userId = userPreferences.userId.first()
    
    if (isUserLoggedIn && userId != null) {
        ApiService.setUserId(userId)
        initializeApp(isLoggedIn = true)
    } else {
        initializeApp(isLoggedIn = false)
    }
}
```

### 2. 🔑 Authentication Flow

#### 📝 Login Process
```mermaid
graph TD
    A[LoginScreen] --> B[User Input Validation]
    B --> C[API Call: /users/login]
    C --> D{Response Code}
    D -->|200| E[Parse User ID]
    D -->|Error| F[Show Error Message]
    E --> G[Save to UserPreferences]
    G --> H[Set UserId in ApiService]
    H --> I[Navigate to Home]
    F --> A
```

**Implementation:**
```kotlin
// LoginScreen.kt
Button(onClick = {
    ApiService.loginUser(username, password) { code, responseBody ->
        if (code == 200 && responseBody != null) {
            val jsonResponse = JSONObject(responseBody)
            val userId = jsonResponse.getString("user_id")
            
            // Save user data
            ApiService.setUserId(userId)
            userPreferences.saveUserData(userId, username)
            
            // Navigate to home
            onLoginSuccess()
        } else {
            // Show error
            showErrorMessage()
        }
    }
})
```

#### 📋 Register Process
```mermaid
graph TD
    A[RegisterScreen] --> B[Input Validation]
    B --> C[Password Confirmation]
    C --> D[API Call: /users/register]
    D --> E{Response Code}
    E -->|200| F[Auto Login]
    E -->|Error| G[Show Error Message]
    F --> H[Navigate to Home]
    G --> A
```

### 3. 🧭 Navigation Flow

#### 🏠 Main Navigation Structure
```mermaid
graph TD
    A[HomeScreen] --> B[BottomNavigation]
    B --> C[Word Genie]
    B --> D[ChatSmart AI]
    B --> E[Visionary Words]
    B --> F[History]
    B --> G[Flashcard]
    
    C --> C1[WordGenieScreen]
    C1 --> C2[VocabInfo]
    
    D --> D1[ChatWelcomeScreen]
    D1 --> D2[ChatScreen]
    
    E --> E1[VisionaryWelcomeScreen]
    E1 --> E2[VisionaryCameraScreen]
    E2 --> E3[VisionaryResultScreen]
    
    F --> F1[HistoryScreen]
    
    G --> G1[FlashcardScreen]
    G1 --> G2[FlashcardDetailScreen]
    G2 --> G3[FlashcardStudyScreen]
```

#### 🔄 Navigation Implementation
```kotlin
// NavGraph.kt
@Composable
fun AppNavGraph(
    navController: NavHostController,
    startDestination: String = Routes.LOGIN,
    isLoggedIn: Boolean = false
) {
    MainScaffold(navController, currentRoute) {
        NavHost(
            navController = navController,
            startDestination = if (isLoggedIn) Routes.HOME else Routes.LOGIN
        ) {
            // Auth routes
            composable(Routes.LOGIN) { LoginScreen() }
            composable(Routes.REGISTER) { RegisterScreen() }
            
            // Main app routes
            composable(Routes.HOME) { HomeScreen() }
            composable(Routes.WORD_GENIE) { WordGenieScreen() }
            // ... other routes
        }
    }
}
```

## 🎯 Feature-Specific Flows

### 📚 Word Genie Flow

```mermaid
graph TD
    A[WordGenieScreen] --> B[User Types Word]
    B --> C[Auto-suggestion from Assets]
    C --> D[User Selects Word]
    D --> E[API Call: /vocabulary/search]
    E --> F{Response}
    F -->|Success| G[Display Results]
    F -->|Error| H[Show Error]
    G --> I[Show: Meaning, IPA, Audio]
    I --> J[Related Words & Synonyms]
    J --> K[User Actions]
    K --> L[Play Audio]
    K --> M[Save to History]
    K --> N[Add to Flashcard]
```

**Implementation Details:**
```kotlin
// WordGenieScreen.kt
LaunchedEffect(searchText) {
    if (searchText.isNotEmpty()) {
        // Auto-suggestion
        suggestions = loadSuggestionsFromAssets(searchText)
    }
}

// Search API call
fun searchWord(word: String) {
    ApiService.searchVocabulary(word) { code, response ->
        if (code == 200) {
            // Parse and display results
            parseVocabularyResponse(response)
        }
    }
}
```

### 💬 ChatSmart AI Flow

```mermaid
graph TD
    A[ChatWelcomeScreen] --> B[Start Chat]
    B --> C[ChatScreen]
    C --> D[User Input Method]
    D --> E[Text Input]
    D --> F[Voice Input]
    
    E --> G[Send Message]
    F --> H[Speech-to-Text API]
    H --> G
    
    G --> I[Add to Chat History]
    I --> J[API Call: /generate-text]
    J --> K[Show Typing Indicator]
    K --> L{AI Response}
    L -->|Success| M[Add AI Message]
    L -->|Error| N[Show Error]
    M --> O[Text-to-Speech]
    O --> P[Play Audio Response]
```

**Voice Input Implementation:**
```kotlin
// RecordingManager.kt
class RecordingManager(private val context: Context) {
    fun startRecording() {
        mediaRecorder = MediaRecorder().apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
            setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
            setOutputFile(audioFile.absolutePath)
            prepare()
            start()
        }
    }
    
    fun stopRecording() {
        mediaRecorder?.apply {
            stop()
            release()
        }
        // Process audio file
        processAudioFile()
    }
}
```

### 👁️ Visionary Words Flow

```mermaid
graph TD
    A[VisionaryWelcomeScreen] --> B[Open Camera]
    B --> C[VisionaryCameraScreen]
    C --> D[Camera Preview]
    D --> E[User Takes Photo]
    E --> F[Image Capture]
    F --> G[VisionaryResultScreen]
    G --> H[Upload Image to API]
    H --> I[API Call: /detect-object]
    I --> J{Detection Results}
    J -->|Success| K[Parse Object Data]
    J -->|Error| L[Show Error]
    K --> M[Draw Bounding Boxes]
    M --> N[Display English Labels]
    N --> O[Show Vietnamese Meanings]
    O --> P[User Actions]
    P --> Q[Play Audio]
    P --> R[Save Word]
    P --> S[Retake Photo]
```

**Camera Implementation:**
```kotlin
// VisionaryCameraScreen.kt
@Composable
fun VisionaryCameraScreen(onPhotoTaken: (Bitmap) -> Unit) {
    val context = LocalContext.current
    val cameraController = remember { LifecycleCameraController(context) }
    
    CameraX(
        controller = cameraController,
        modifier = Modifier.fillMaxSize()
    )
    
    // Capture button
    FloatingActionButton(
        onClick = {
            captureImage(cameraController) { bitmap ->
                onPhotoTaken(bitmap)
            }
        }
    )
}
```

### 📖 History Management Flow

```mermaid
graph TD
    A[HistoryScreen] --> B[Load Initial Data]
    B --> C[API Call: /vocabulary/list]
    C --> D[Display List with Pagination]
    D --> E[User Scrolls]
    E --> F{Reached End?}
    F -->|Yes| G[Load More Data]
    F -->|No| E
    G --> H[Increase Page Size]
    H --> I[API Call with New Page Size]
    I --> J[Append New Data]
    J --> D
    
    D --> K[User Actions]
    K --> L[Delete Word]
    K --> M[Add to Flashcard]
    K --> N[Search/Filter]
```

**Lazy Loading Implementation:**
```kotlin
// History.kt
fun loadMoreData() {
    if (isLoadingMore || !hasMoreData) return
    
    isLoadingMore = true
    val newPageSize = currentPageSize + 5
    
    ApiService.getVocabularyList(
        pageSize = newPageSize,
        pageIndex = 0
    ) { code, body ->
        if (code == 200) {
            // Update list with new data
            updateHistoryList(body)
            currentPageSize = newPageSize
        }
        isLoadingMore = false
    }
}
```

### 🃏 Flashcard Management Flow

```mermaid
graph TD
    A[FlashcardScreen] --> B[Display Flashcard Sets]
    B --> C[User Actions]
    C --> D[Create New Set]
    C --> E[Edit Existing Set]
    C --> F[Import from Quizlet]
    C --> G[Study Mode]
    
    D --> H[AddFlashcardDialog]
    H --> I[Save to DataStore]
    
    E --> J[FlashcardDetailScreen]
    J --> K[CRUD Operations]
    K --> I
    
    F --> L[ImportFromQuizletDialog]
    L --> M[Parse Quizlet URL]
    M --> N[Scrape Card Data]
    N --> O[Batch Import]
    O --> I
    
    G --> P[FlashcardStudyScreen]
    P --> Q[Flip Animation]
    Q --> R[Progress Tracking]
```

**Quizlet Import Implementation:**
```kotlin
// QuizletImporter.kt
suspend fun importFromUrl(url: String): List<Flashcard> {
    return withContext(Dispatchers.IO) {
        val setId = extractSetId(url)
        val doc = Jsoup.connect("https://quizlet.com/$setId")
            .userAgent("Mozilla/5.0...")
            .get()
        
        // Parse HTML and extract cards
        val cards = parseQuizletCards(doc)
        cards
    }
}
```

## 🔄 State Management Flow

### 📊 Data Flow Pattern
```mermaid
graph TD
    A[UI Event] --> B[ViewModel]
    B --> C[Repository]
    C --> D[Data Source]
    D --> E[API/DataStore]
    E --> F[Response]
    F --> C
    C --> B
    B --> G[UI State Update]
    G --> H[Recomposition]
```

### 🎯 State Management Examples

#### FlashcardViewModel State Flow
```kotlin
class FlashcardViewModel : ViewModel() {
    private val _flashcardSets = MutableStateFlow<List<FlashcardSet>>(emptyList())
    val flashcardSets: StateFlow<List<FlashcardSet>> = _flashcardSets.asStateFlow()
    
    private val _currentSet = MutableStateFlow<FlashcardSet?>(null)
    val currentSet: StateFlow<FlashcardSet?> = _currentSet.asStateFlow()
    
    fun addFlashcard(setId: String, front: String, back: String, ipa: String) {
        repository.addFlashcard(setId, front, back, ipa)
    }
}
```

#### Compose State Management
```kotlin
@Composable
fun ChatScreen() {
    var messages by remember { mutableStateOf(listOf<ChatMessage>()) }
    var isWaitingForResponse by remember { mutableStateOf(false) }
    var currentMessage by remember { mutableStateOf("") }
    
    LaunchedEffect(messages) {
        if (messages.isNotEmpty() && messages.last().isUser) {
            // Send to AI
            sendToAI(messages.last().text)
        }
    }
}
```

## 🔧 Error Handling Flow

### 🚨 Error Handling Strategy
```mermaid
graph TD
    A[API Call] --> B{Network Available?}
    B -->|No| C[Show Offline Message]
    B -->|Yes| D[Make Request]
    D --> E{Response Code}
    E -->|200| F[Success Flow]
    E -->|4xx| G[Client Error]
    E -->|5xx| H[Server Error]
    E -->|Timeout| I[Timeout Error]
    
    G --> J[Show User-Friendly Message]
    H --> K[Show Retry Option]
    I --> L[Show Retry Option]
    
    K --> M[Retry Logic]
    L --> M
    M --> D
```

### 🛡️ Error Handling Implementation
```kotlin
// ApiService.kt
fun makeApiCall(request: Request, callback: (Int, String?) -> Unit) {
    client.newCall(request).enqueue(object : Callback {
        override fun onFailure(call: Call, e: IOException) {
            when (e) {
                is SocketTimeoutException -> callback(-1, "Timeout")
                is UnknownHostException -> callback(-2, "No Internet")
                else -> callback(-3, e.message)
            }
        }
        
        override fun onResponse(call: Call, response: Response) {
            callback(response.code, response.body?.string())
        }
    })
}
```

---

*Luồng xử lý này đảm bảo user experience mượt mà và xử lý tất cả edge cases có thể xảy ra.*
