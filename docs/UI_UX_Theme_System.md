# 🎨 LingoAI - UI/UX và Theme System Documentation

## 🌟 Design Philosophy

LingoAI được thiết kế theo nguyên tắc **Material Design 3** với focus vào **user-friendly learning experience**, sử dụng **Jetpack Compose** để tạo ra UI hiện đại và responsive.

### 🎯 Design Principles
- **Simplicity**: Interface đơn giản, d<PERSON> sử dụng
- **Consistency**: Consistent design language across all screens
- **Accessibility**: Hỗ trợ accessibility features
- **Performance**: Smooth animations và fast interactions
- **Responsive**: Adaptive layout cho các screen sizes

## 🎨 Theme System Architecture

### 📁 Theme Structure
```
ui/theme/
├── 📄 Theme.kt          # Main theme configuration
├── 📄 Color.kt          # Color definitions
└── 📄 Type.kt           # Typography system
```

### 🌈 Color System

#### Primary Color Palette
```kotlin
// Main brand colors
val MainColor = Color(0xFFF2CEDF)        // Soft pink background
val ButtonPrimary = Color(0xFFD17878)    // Primary action color
val ButtonSecondary = Color(0xFFD9D9D9)  // Secondary/neutral color

// Success & Error colors
val ButtonSuccess = Color(0xFF4CAF50)    // Success actions
val ButtonDanger = Color(0xFFFF5722)     // Destructive actions
```

#### Text & Icon Colors
```kotlin
// Text hierarchy
val TextPrimary = Color.Black            // Primary text
val TextSecondary = Color.Gray           // Secondary text
val IconPrimary = Color.Black            // Primary icons
val IconSecondary = Color.Black.copy(alpha = 0.5f)  // Secondary icons
```

#### Feature-Specific Colors
```kotlin
// Chat interface
val ChatUserBubble = Color(0xFFE3F2FD)   // User message background
val ChatBotBubble = Color(0xFFD1C4E9)    // AI message background
val ChatBotDotColor = Color(0xFF6A1B9A)  // Typing indicator

// Recording states
val RecordingActive = Color(0xFFE57373)   // Recording in progress
val RecordingProcessing = Color(0xFF81C784)  // Processing audio
val RecordingDefault = Color(0xFFD9D9D9)  // Default state

// UI Components
val CardBackground = Color(0xFFD9D9D9)    // Card backgrounds
val TextFieldBackground = Color(0xFFD9D9D9)  // Input fields
val ProgressBarColor = Color(0xFFE48ED4)  // Progress indicators
```

#### Tab & Navigation Colors
```kotlin
// Vocabulary info tabs
val TabSelected = Color.White            // Active tab
val TabUnselected = Color(0xFFE0BFD6)    // Inactive tab
val WordItemBackground = Color(0xFFD9D9D9)  // Word list items

// Home screen
val HomeButtonBackground = Color(0xFFD17878)  // Feature buttons
val HomeButtonText = Color.White         // Button text
val HomeSubtitleText = Color(0xFFD17878) // Subtitle text
```

### 🔤 Typography System

#### Font Family
```kotlin
val RobotoFontFamily = FontFamily(
    Font(R.font.roboto_regular, FontWeight.Normal),
    Font(R.font.roboto_bold, FontWeight.Bold)
)
```

#### Typography Styles
```kotlin
val Typography = Typography(
    // Body text
    bodyLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp
    ),
    
    // Titles
    titleLarge = TextStyle(
        fontFamily = RobotoFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 22.sp
    )
)
```

### 🌓 Theme Configuration

#### Main Theme Implementation
```kotlin
@Composable
fun MyApplicationTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) 
            else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
```

## 🧩 Component Architecture

### 🏗️ Layout Components

#### MainScaffold - Core Layout Wrapper
```kotlin
@Composable
fun MainScaffold(
    navController: NavHostController,
    currentRoute: String,
    content: @Composable () -> Unit
) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = Color.Transparent,
        bottomBar = {
            if (shouldShowBottomBar(currentRoute)) {
                BottomNavBar(
                    currentRoute = currentRoute.split("/")[0],
                    onNavItemSelected = { route ->
                        handleBottomNavigation(navController, route)
                    }
                )
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            content()
        }
    }
}
```

#### BottomNavBar - Navigation Component
```kotlin
@Composable
fun BottomNavBar(
    currentRoute: String, 
    onNavItemSelected: (String) -> Unit,
    modifier: Modifier
) {
    val navItems = listOf(
        NavItem("Word", "word_genie", Icons.Filled.MenuBook, Icons.Outlined.MenuBook),
        NavItem("Chat", "chat_smart_ai", Icons.Filled.Chat, Icons.Outlined.Chat),
        NavItem("Vision", "visionary_words", Icons.Filled.Visibility, Icons.Outlined.Visibility),
        NavItem("History", "history", Icons.Filled.Schedule, Icons.Outlined.Schedule),
        NavItem("Cards", "flashcard", Icons.Filled.Style, Icons.Outlined.Style)
    )

    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(vertical = 8.dp, horizontal = 4.dp),
        horizontalArrangement = Arrangement.spacedBy(0.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        navItems.forEach { item ->
            NavBarItem(
                item = item,
                isSelected = currentRoute == item.route,
                onClick = { onNavItemSelected(item.route) },
                modifier = Modifier.weight(1f)
            )
        }
    }
}
```

### 🎭 Animation System

#### Navigation Item Animation
```kotlin
@Composable
fun NavBarItem(
    item: NavItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Scale animation
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "scale"
    )

    // Background alpha animation
    val backgroundAlpha by animateFloatAsState(
        targetValue = if (isSelected) 1f else 0f,
        animationSpec = tween(300),
        label = "background"
    )

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .scale(scale)
            .clip(RoundedCornerShape(12.dp))
            .background(
                brush = if (isSelected) {
                    Brush.verticalGradient(
                        colors = listOf(
                            ButtonPrimary.copy(alpha = backgroundAlpha * 0.2f),
                            ButtonPrimary.copy(alpha = backgroundAlpha * 0.1f)
                        )
                    )
                } else {
                    Brush.verticalGradient(
                        colors = listOf(Color.Transparent, Color.Transparent)
                    )
                }
            )
            .clickable { onClick() }
            .padding(vertical = 6.dp)
    ) {
        Icon(
            imageVector = if (isSelected) item.selectedIcon else item.unselectedIcon,
            contentDescription = item.label,
            tint = if (isSelected) ButtonPrimary else TextSecondary,
            modifier = Modifier.size(20.dp)
        )
        
        Text(
            text = item.label,
            fontSize = 10.sp,
            color = if (isSelected) ButtonPrimary else TextSecondary,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}
```

### 👤 User Interface Components

#### User Profile Components
```kotlin
@Composable
fun UserInfoSection(name: String, email: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text("👤 Họ tên: $name", style = MaterialTheme.typography.titleMedium)
            Text("📧 Email: $email", style = MaterialTheme.typography.bodyMedium)
        }
    }
}

@Composable
fun LearningProgressSection(progress: Float) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text("🎯 Tiến độ học tập", style = MaterialTheme.typography.titleMedium)
            Spacer(Modifier.height(8.dp))
            LinearProgressIndicator(
                progress = progress, 
                modifier = Modifier.fillMaxWidth(),
                color = ProgressBarColor,
                trackColor = ProgressBarTrackColor
            )
            Spacer(Modifier.height(4.dp))
            Text("${(progress * 100).toInt()}%", style = MaterialTheme.typography.bodyMedium)
        }
    }
}
```

#### User Header with Background
```kotlin
@Composable
fun UserHeader(name: String, email: String) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp)
    ) {
        // Background image
        Image(
            painter = painterResource(id = R.drawable.bg_header),
            contentDescription = "Background",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize()
        )

        // Avatar section
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .offset(y = 40.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.avatar),
                contentDescription = "Avatar",
                modifier = Modifier
                    .size(100.dp)
                    .clip(CircleShape)
                    .border(3.dp, MaterialTheme.colorScheme.background, CircleShape)
            )

            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = name,
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onBackground
            )
        }
    }
}
```

### 🎓 Learning Components

#### Tiny Lesson Components
```kotlin
@Composable
fun TinyLessonCard(
    title: String,
    description: String,
    progress: Float,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = CardBackground)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = TextPrimary
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = TextSecondary
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            LinearProgressIndicator(
                progress = progress,
                modifier = Modifier.fillMaxWidth(),
                color = ProgressBarColor,
                trackColor = ProgressBarTrackColor
            )
        }
    }
}
```

### 📷 Camera Components

#### Camera Overlay Components
```kotlin
@Composable
fun CameraOverlay(
    onCaptureClick: () -> Unit,
    onBackClick: () -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // Back button
        IconButton(
            onClick = onBackClick,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "Back",
                tint = Color.White
            )
        }
        
        // Capture button
        FloatingActionButton(
            onClick = onCaptureClick,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(32.dp),
            containerColor = ButtonPrimary
        ) {
            Icon(
                imageVector = Icons.Default.Camera,
                contentDescription = "Capture",
                tint = Color.White
            )
        }
    }
}
```

## 🎯 Screen-Specific Design Patterns

### 🏠 Home Screen Design
```kotlin
@Composable
fun HomeScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(MainColor, Color.White)
                )
            )
            .padding(16.dp)
    ) {
        // Welcome section
        WelcomeSection()
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Feature grid
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(featureItems) { feature ->
                FeatureCard(feature)
            }
        }
    }
}
```

### 💬 Chat Interface Design
```kotlin
@Composable
fun ChatBubble(
    message: ChatMessage,
    onPlayAudio: (String) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = if (message.isUser) 
            Arrangement.End else Arrangement.Start
    ) {
        Card(
            modifier = Modifier.widthIn(max = 280.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (message.isUser) 
                    ChatUserBubble else ChatBotBubble
            ),
            shape = RoundedCornerShape(
                topStart = 16.dp,
                topEnd = 16.dp,
                bottomStart = if (message.isUser) 16.dp else 4.dp,
                bottomEnd = if (message.isUser) 4.dp else 16.dp
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = message.text,
                    style = MaterialTheme.typography.bodyMedium,
                    color = TextPrimary
                )
                
                if (!message.isUser) {
                    Spacer(modifier = Modifier.height(8.dp))
                    IconButton(
                        onClick = { onPlayAudio(message.text) },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.VolumeUp,
                            contentDescription = "Play Audio",
                            tint = ButtonPrimary
                        )
                    }
                }
            }
        }
    }
}
```

### 🃏 Flashcard Design
```kotlin
@Composable
fun FlashcardItem(
    flashcard: Flashcard,
    isFlipped: Boolean,
    onFlip: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp)
            .clickable { onFlip() },
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            AnimatedContent(
                targetState = isFlipped,
                transitionSpec = {
                    slideInHorizontally { it } + fadeIn() with
                    slideOutHorizontally { -it } + fadeOut()
                }
            ) { flipped ->
                if (!flipped) {
                    // Front side
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = flashcard.front,
                            style = MaterialTheme.typography.titleLarge,
                            textAlign = TextAlign.Center
                        )
                        if (flashcard.ipa.isNotEmpty()) {
                            Text(
                                text = "/${flashcard.ipa}/",
                                style = MaterialTheme.typography.bodyMedium,
                                color = TextSecondary
                            )
                        }
                    }
                } else {
                    // Back side
                    Text(
                        text = flashcard.back,
                        style = MaterialTheme.typography.titleMedium,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}
```

## 📱 Responsive Design

### 🔧 Adaptive Layout
```kotlin
@Composable
fun AdaptiveLayout(
    content: @Composable (WindowSizeClass) -> Unit
) {
    val windowSizeClass = calculateWindowSizeClass()
    
    content(windowSizeClass)
}

@Composable
fun ResponsiveGrid(items: List<Any>) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    
    val columns = when {
        screenWidth < 600.dp -> 1
        screenWidth < 900.dp -> 2
        else -> 3
    }
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(columns)
    ) {
        items(items) { item ->
            // Item content
        }
    }
}
```

### 📐 Dimension System
```kotlin
object Dimensions {
    val paddingSmall = 8.dp
    val paddingMedium = 16.dp
    val paddingLarge = 24.dp
    
    val cornerRadiusSmall = 4.dp
    val cornerRadiusMedium = 8.dp
    val cornerRadiusLarge = 16.dp
    
    val elevationSmall = 2.dp
    val elevationMedium = 4.dp
    val elevationLarge = 8.dp
}
```

## ♿ Accessibility Features

### 🔍 Accessibility Implementation
```kotlin
@Composable
fun AccessibleButton(
    text: String,
    onClick: () -> Unit,
    contentDescription: String? = null
) {
    Button(
        onClick = onClick,
        modifier = Modifier.semantics {
            contentDescription?.let {
                this.contentDescription = it
            }
            role = Role.Button
        }
    ) {
        Text(text)
    }
}

@Composable
fun AccessibleImage(
    painter: Painter,
    contentDescription: String,
    modifier: Modifier = Modifier
) {
    Image(
        painter = painter,
        contentDescription = contentDescription,
        modifier = modifier.semantics {
            this.contentDescription = contentDescription
        }
    )
}
```

---

*UI/UX system này đảm bảo consistent, accessible và engaging user experience cho LingoAI.*
