# 🤖 LingoAI - AI Features Integration Documentation

## 🌟 AI Features Overview

LingoAI tích hợp nhiều công nghệ AI tiên tiến để tạo ra trải nghiệm học tiếng Anh tương tác và thông minh:

1. **🎤 Speech-to-Text**: <PERSON><PERSON><PERSON><PERSON> diện giọng nói thành text
2. **🔊 Text-to-Speech**: Chuyển text thành giọng nói tự nhiên
3. **👁️ Object Detection**: Nhận diện vật thể qua camera
4. **💬 Natural Language Generation**: AI chat thông minh
5. **📚 Vocabulary Intelligence**: Gợi ý từ vựng thông minh

## 🎤 Speech-to-Text Integration

### 🏗️ Architecture
```mermaid
graph TD
    A[User Voice Input] --> B[RecordingManager]
    B --> C[MediaRecorder]
    C --> D[Audio File]
    D --> E[API Upload]
    E --> F[Speech-to-Text Service]
    F --> G[Text Result]
    G --> H[UI Update]
```

### 🎙️ RecordingManager Implementation

```kotlin
class RecordingManager(private val context: Context) {
    private var mediaRecorder: MediaRecorder? = null
    private var audioFile: File? = null
    private var isRecording = false
    
    // Recording states
    enum class RecordingState {
        IDLE, RECORDING, PROCESSING, COMPLETED, ERROR
    }
    
    private val _recordingState = MutableStateFlow(RecordingState.IDLE)
    val recordingState: StateFlow<RecordingState> = _recordingState.asStateFlow()
    
    fun startRecording() {
        try {
            // Create temporary audio file
            audioFile = File(context.cacheDir, "temp_audio_${System.currentTimeMillis()}.3gp")
            
            mediaRecorder = MediaRecorder().apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
                setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
                setOutputFile(audioFile!!.absolutePath)
                
                prepare()
                start()
            }
            
            isRecording = true
            _recordingState.value = RecordingState.RECORDING
            Log.d("RecordingManager", "Recording started")
            
        } catch (e: Exception) {
            Log.e("RecordingManager", "Failed to start recording: ${e.message}")
            _recordingState.value = RecordingState.ERROR
        }
    }
    
    fun stopRecording(onResult: (String?) -> Unit) {
        try {
            mediaRecorder?.apply {
                stop()
                release()
            }
            mediaRecorder = null
            isRecording = false
            
            _recordingState.value = RecordingState.PROCESSING
            
            // Process audio file
            audioFile?.let { file ->
                processAudioFile(file, onResult)
            } ?: onResult(null)
            
        } catch (e: Exception) {
            Log.e("RecordingManager", "Failed to stop recording: ${e.message}")
            _recordingState.value = RecordingState.ERROR
            onResult(null)
        }
    }
    
    private fun processAudioFile(file: File, onResult: (String?) -> Unit) {
        // Upload to speech-to-text API
        ApiService.speechToText(file) { result ->
            _recordingState.value = if (result != null) {
                RecordingState.COMPLETED
            } else {
                RecordingState.ERROR
            }
            onResult(result)
        }
    }
}
```

### 🎯 Speech-to-Text API Integration

```kotlin
// ApiService.kt
fun speechToText(audioFile: File, callback: (String?) -> Unit) {
    try {
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(
                "audio", 
                audioFile.name, 
                audioFile.asRequestBody("audio/3gp".toMediaType())
            )
            .build()

        val request = Request.Builder()
            .url("$BASE_URL/speech-to-text")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("ApiService", "Speech-to-text failed: ${e.message}")
                callback(null)
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    if (response.isSuccessful && responseBody != null) {
                        val json = JSONObject(responseBody)
                        val transcription = json.optString("transcription", "")
                        callback(if (transcription.isNotEmpty()) transcription else null)
                    } else {
                        callback(null)
                    }
                } catch (e: Exception) {
                    Log.e("ApiService", "Error parsing speech-to-text response: ${e.message}")
                    callback(null)
                }
            }
        })
    } catch (e: Exception) {
        Log.e("ApiService", "Speech-to-text request failed: ${e.message}")
        callback(null)
    }
}
```

### 🎤 UI Integration

```kotlin
@Composable
fun VoiceInputButton(
    recordingManager: RecordingManager,
    onTranscription: (String) -> Unit
) {
    val recordingState by recordingManager.recordingState.collectAsState()
    
    val buttonColor = when (recordingState) {
        RecordingState.IDLE -> RecordingDefault
        RecordingState.RECORDING -> RecordingActive
        RecordingState.PROCESSING -> RecordingProcessing
        RecordingState.COMPLETED -> ButtonSuccess
        RecordingState.ERROR -> ButtonDanger
    }
    
    FloatingActionButton(
        onClick = {
            when (recordingState) {
                RecordingState.IDLE -> {
                    recordingManager.startRecording()
                }
                RecordingState.RECORDING -> {
                    recordingManager.stopRecording { result ->
                        result?.let { onTranscription(it) }
                    }
                }
                else -> { /* Do nothing during processing */ }
            }
        },
        containerColor = buttonColor,
        modifier = Modifier.size(64.dp)
    ) {
        when (recordingState) {
            RecordingState.IDLE -> Icon(Icons.Default.Mic, "Start Recording")
            RecordingState.RECORDING -> Icon(Icons.Default.Stop, "Stop Recording")
            RecordingState.PROCESSING -> CircularProgressIndicator(color = Color.White)
            RecordingState.COMPLETED -> Icon(Icons.Default.Check, "Completed")
            RecordingState.ERROR -> Icon(Icons.Default.Error, "Error")
        }
    }
}
```

## 🔊 Text-to-Speech Integration

### 🎵 AudioManager Implementation

```kotlin
class AudioManager(private val context: Context) {
    private var mediaPlayer: MediaPlayer? = null
    private var textToSpeech: TextToSpeech? = null
    private var isTtsInitialized = false
    
    init {
        initializeTextToSpeech()
    }
    
    private fun initializeTextToSpeech() {
        textToSpeech = TextToSpeech(context) { status ->
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech?.language = Locale.US
                isTtsInitialized = true
                Log.d("AudioManager", "TTS initialized successfully")
            } else {
                Log.e("AudioManager", "TTS initialization failed")
            }
        }
    }
    
    fun playTextToSpeech(text: String) {
        if (isTtsInitialized) {
            textToSpeech?.speak(text, TextToSpeech.QUEUE_FLUSH, null, null)
        } else {
            Log.w("AudioManager", "TTS not initialized")
        }
    }
    
    fun playAudioFromUrl(url: String) {
        try {
            mediaPlayer?.release()
            mediaPlayer = MediaPlayer().apply {
                setDataSource(url)
                setOnPreparedListener { start() }
                setOnCompletionListener { release() }
                prepareAsync()
            }
        } catch (e: Exception) {
            Log.e("AudioManager", "Failed to play audio: ${e.message}")
        }
    }
    
    fun playAudioFromText(text: String) {
        // First try TTS
        if (isTtsInitialized) {
            playTextToSpeech(text)
        } else {
            // Fallback to API-generated audio
            generateAndPlayAudio(text)
        }
    }
    
    private fun generateAndPlayAudio(text: String) {
        ApiService.generateAudio(text) { audioUrl ->
            audioUrl?.let { playAudioFromUrl(it) }
        }
    }
    
    fun release() {
        mediaPlayer?.release()
        textToSpeech?.shutdown()
    }
}
```

### 🎵 Audio API Integration

```kotlin
// ApiService.kt
fun generateAudio(text: String, callback: (String?) -> Unit) {
    try {
        val json = """
            {
              "text": "$text",
              "voice": "en-US",
              "speed": 1.0
            }
        """.trimIndent()

        val body = json.toRequestBody("application/json".toMediaType())
        val request = Request.Builder()
            .url("$BASE_URL/text-to-speech")
            .post(body)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("ApiService", "Audio generation failed: ${e.message}")
                callback(null)
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    if (response.isSuccessful && responseBody != null) {
                        val json = JSONObject(responseBody)
                        val audioUrl = json.optString("audio_url", "")
                        callback(if (audioUrl.isNotEmpty()) audioUrl else null)
                    } else {
                        callback(null)
                    }
                } catch (e: Exception) {
                    Log.e("ApiService", "Error parsing audio response: ${e.message}")
                    callback(null)
                }
            }
        })
    } catch (e: Exception) {
        Log.e("ApiService", "Audio generation request failed: ${e.message}")
        callback(null)
    }
}
```

## 👁️ Object Detection Integration

### 📷 Camera Integration

```kotlin
@Composable
fun VisionaryCameraScreen(onPhotoTaken: (Bitmap) -> Unit) {
    val context = LocalContext.current
    val cameraController = remember { LifecycleCameraController(context) }
    val lifecycleOwner = LocalLifecycleOwner.current
    
    LaunchedEffect(cameraController) {
        cameraController.bindToLifecycle(lifecycleOwner)
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        AndroidView(
            factory = { context ->
                PreviewView(context).apply {
                    controller = cameraController
                    scaleType = PreviewView.ScaleType.FILL_CENTER
                }
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // Camera controls overlay
        CameraControls(
            onCaptureClick = {
                captureImage(cameraController) { bitmap ->
                    onPhotoTaken(bitmap)
                }
            }
        )
    }
}

private fun captureImage(
    cameraController: LifecycleCameraController,
    onImageCaptured: (Bitmap) -> Unit
) {
    val outputFileOptions = ImageCapture.OutputFileOptions.Builder(
        File(context.cacheDir, "temp_image_${System.currentTimeMillis()}.jpg")
    ).build()
    
    cameraController.takePicture(
        outputFileOptions,
        ContextCompat.getMainExecutor(context),
        object : ImageCapture.OnImageSavedCallback {
            override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                output.savedUri?.let { uri ->
                    val bitmap = loadBitmapFromUri(context, uri)
                    bitmap?.let { onImageCaptured(it) }
                }
            }
            
            override fun onError(exception: ImageCaptureException) {
                Log.e("Camera", "Image capture failed: ${exception.message}")
            }
        }
    )
}
```

### 🔍 Object Detection API

```kotlin
// ApiService.kt
fun detectObject(imageFile: File, callback: (String?) -> Unit) {
    try {
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(
                "image", 
                imageFile.name, 
                imageFile.asRequestBody("image/jpeg".toMediaType())
            )
            .build()

        val request = Request.Builder()
            .url("$BASE_URL/detect-object")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("ApiService", "Object detection failed: ${e.message}")
                callback(null)
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    callback(responseBody)
                } catch (e: Exception) {
                    Log.e("ApiService", "Error processing detection response: ${e.message}")
                    callback(null)
                }
            }
        })
    } catch (e: Exception) {
        Log.e("ApiService", "Object detection request failed: ${e.message}")
        callback(null)
    }
}
```

### 🎯 Detection Result Processing

```kotlin
data class DetectedObject(
    val label: String,
    val confidence: Float,
    val bbox: BoundingBox,
    val vietnameseMeaning: String
)

data class BoundingBox(
    val x: Float,
    val y: Float,
    val width: Float,
    val height: Float
)

fun parseDetectionResponse(response: String): List<DetectedObject> {
    return try {
        val json = JSONObject(response)
        val objectsArray = json.getJSONArray("objects")
        val detectedObjects = mutableListOf<DetectedObject>()
        
        for (i in 0 until objectsArray.length()) {
            val obj = objectsArray.getJSONObject(i)
            val bbox = obj.getJSONObject("bbox")
            
            detectedObjects.add(
                DetectedObject(
                    label = obj.getString("label"),
                    confidence = obj.getDouble("confidence").toFloat(),
                    bbox = BoundingBox(
                        x = bbox.getDouble("x").toFloat(),
                        y = bbox.getDouble("y").toFloat(),
                        width = bbox.getDouble("width").toFloat(),
                        height = bbox.getDouble("height").toFloat()
                    ),
                    vietnameseMeaning = obj.optString("vietnamese_meaning", "")
                )
            )
        }
        
        detectedObjects
    } catch (e: Exception) {
        Log.e("ObjectDetection", "Failed to parse detection response: ${e.message}")
        emptyList()
    }
}
```

### 🖼️ Detection Result Display

```kotlin
@Composable
fun DetectionResultOverlay(
    image: Bitmap,
    detectedObjects: List<DetectedObject>,
    onPlayAudio: (String) -> Unit,
    onSaveWord: (String) -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // Display image
        Image(
            bitmap = image.asImageBitmap(),
            contentDescription = "Detected Image",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Fit
        )
        
        // Overlay bounding boxes and labels
        Canvas(modifier = Modifier.fillMaxSize()) {
            detectedObjects.forEach { obj ->
                // Draw bounding box
                drawRect(
                    color = Color.Red,
                    topLeft = Offset(obj.bbox.x, obj.bbox.y),
                    size = Size(obj.bbox.width, obj.bbox.height),
                    style = Stroke(width = 3.dp.toPx())
                )
                
                // Draw label background
                drawRect(
                    color = Color.Red,
                    topLeft = Offset(obj.bbox.x, obj.bbox.y - 30.dp.toPx()),
                    size = Size(obj.bbox.width, 30.dp.toPx())
                )
            }
        }
        
        // Interactive labels
        detectedObjects.forEach { obj ->
            DetectionLabel(
                obj = obj,
                onPlayAudio = onPlayAudio,
                onSaveWord = onSaveWord,
                modifier = Modifier.offset(
                    x = obj.bbox.x.dp,
                    y = (obj.bbox.y - 30).dp
                )
            )
        }
    }
}

@Composable
fun DetectionLabel(
    obj: DetectedObject,
    onPlayAudio: (String) -> Unit,
    onSaveWord: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.8f))
    ) {
        Row(
            modifier = Modifier.padding(4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${obj.label} (${obj.vietnameseMeaning})",
                color = Color.White,
                fontSize = 12.sp
            )
            
            IconButton(
                onClick = { onPlayAudio(obj.label) },
                modifier = Modifier.size(20.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.VolumeUp,
                    contentDescription = "Play Audio",
                    tint = Color.White
                )
            }
            
            IconButton(
                onClick = { onSaveWord(obj.label) },
                modifier = Modifier.size(20.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Save,
                    contentDescription = "Save Word",
                    tint = Color.White
                )
            }
        }
    }
}
```

## 💬 Natural Language Generation

### 🤖 AI Chat Integration

```kotlin
// ApiService.kt
fun generateText(prompt: String, callback: (Int, String?) -> Unit) {
    try {
        val json = """
            {
              "prompt": "$prompt",
              "max_tokens": 150,
              "temperature": 0.7,
              "user_id": "$USER_ID"
            }
        """.trimIndent()

        val body = json.toRequestBody("application/json".toMediaType())
        val request = Request.Builder()
            .url("$BASE_URL/generate-text")
            .post(body)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("ApiService", "Text generation failed: ${e.message}")
                callback(-1, e.message)
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    callback(response.code, responseBody)
                } catch (e: Exception) {
                    Log.e("ApiService", "Error processing text generation response: ${e.message}")
                    callback(-1, e.message)
                }
            }
        })
    } catch (e: Exception) {
        Log.e("ApiService", "Text generation request failed: ${e.message}")
        callback(-1, e.message)
    }
}
```

### 💭 Chat Flow Implementation

```kotlin
@Composable
fun ChatScreen(recordingManager: RecordingManager) {
    var messages by remember { mutableStateOf(listOf<ChatMessage>()) }
    var isWaitingForResponse by remember { mutableStateOf(false) }
    var currentMessage by remember { mutableStateOf("") }
    
    // Handle new user messages
    LaunchedEffect(messages) {
        val lastMessage = messages.lastOrNull()
        if (lastMessage?.isUser == true && !isWaitingForResponse) {
            isWaitingForResponse = true
            
            ApiService.generateText(lastMessage.text) { code, response ->
                isWaitingForResponse = false
                
                if (code == 200 && response != null) {
                    try {
                        val json = JSONObject(response)
                        val textResponse = json.optString("response", "")
                        if (textResponse.isNotEmpty()) {
                            val aiMessage = ChatMessage("Lingoo", textResponse, false)
                            messages = messages + aiMessage
                        }
                    } catch (e: Exception) {
                        Log.e("ChatScreen", "Error parsing AI response: ${e.message}")
                    }
                }
            }
        }
    }
    
    Column(modifier = Modifier.fillMaxSize()) {
        // Chat messages
        LazyColumn(
            modifier = Modifier.weight(1f),
            reverseLayout = true
        ) {
            if (isWaitingForResponse) {
                item { TypingIndicator() }
            }
            
            items(messages.reversed()) { message ->
                ChatBubble(
                    message = message,
                    onPlayAudio = { text ->
                        recordingManager.playAudioFromText(text)
                    }
                )
            }
        }
        
        // Input area
        ChatInputArea(
            currentMessage = currentMessage,
            onMessageChange = { currentMessage = it },
            onSendMessage = {
                if (currentMessage.isNotEmpty()) {
                    messages = messages + ChatMessage("User", currentMessage, true)
                    currentMessage = ""
                }
            },
            recordingManager = recordingManager,
            onVoiceInput = { transcription ->
                currentMessage = transcription
            }
        )
    }
}
```

## 📚 Vocabulary Intelligence

### 🧠 Smart Word Suggestions

```kotlin
class WordSuggestionEngine(private val context: Context) {
    private var wordList: List<String> = emptyList()
    
    init {
        loadWordDatabase()
    }
    
    private fun loadWordDatabase() {
        try {
            val inputStream = context.assets.open("english_words.txt")
            wordList = inputStream.bufferedReader().readLines()
            Log.d("WordSuggestion", "Loaded ${wordList.size} words")
        } catch (e: Exception) {
            Log.e("WordSuggestion", "Failed to load word database: ${e.message}")
        }
    }
    
    fun getSuggestions(query: String, limit: Int = 10): List<String> {
        if (query.length < 2) return emptyList()
        
        return wordList
            .filter { it.startsWith(query, ignoreCase = true) }
            .take(limit)
    }
    
    fun getRelatedWords(word: String): List<String> {
        // Simple related word logic - can be enhanced with ML
        return wordList
            .filter { 
                it.contains(word.take(3), ignoreCase = true) && 
                it != word 
            }
            .take(5)
    }
}
```

### 🎯 Intelligent Word Processing

```kotlin
fun processVocabularyResponse(response: String): VocabularyItem? {
    return try {
        val json = JSONObject(response)
        VocabularyItem(
            word = json.optString("word", ""),
            ipa = json.optString("ipa", ""),
            meaning = json.optString("meaning", ""),
            synonyms = parseStringArray(json.optJSONArray("synonyms")),
            antonyms = parseStringArray(json.optJSONArray("antonyms")),
            relatedWords = parseStringArray(json.optJSONArray("related_words")),
            audioUrl = json.optString("audio_url", null)
        )
    } catch (e: Exception) {
        Log.e("VocabularyProcessor", "Failed to parse vocabulary response: ${e.message}")
        null
    }
}

private fun parseStringArray(jsonArray: JSONArray?): List<String> {
    return try {
        jsonArray?.let { array ->
            (0 until array.length()).map { array.getString(it) }
        } ?: emptyList()
    } catch (e: Exception) {
        emptyList()
    }
}
```

## 🔧 AI Performance Optimization

### ⚡ Caching Strategy
```kotlin
object AICache {
    private val speechCache = LruCache<String, String>(50)
    private val vocabularyCache = LruCache<String, VocabularyItem>(100)
    
    fun cacheSpeechResult(audioHash: String, transcription: String) {
        speechCache.put(audioHash, transcription)
    }
    
    fun getCachedSpeech(audioHash: String): String? {
        return speechCache.get(audioHash)
    }
    
    fun cacheVocabulary(word: String, item: VocabularyItem) {
        vocabularyCache.put(word.lowercase(), item)
    }
    
    fun getCachedVocabulary(word: String): VocabularyItem? {
        return vocabularyCache.get(word.lowercase())
    }
}
```

### 🚀 Async Processing
```kotlin
class AIProcessingManager {
    private val processingScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    fun processInBackground(
        operation: suspend () -> Unit,
        onComplete: () -> Unit = {}
    ) {
        processingScope.launch {
            try {
                operation()
                withContext(Dispatchers.Main) {
                    onComplete()
                }
            } catch (e: Exception) {
                Log.e("AIProcessing", "Background processing failed: ${e.message}")
            }
        }
    }
}
```

---

*AI features integration này tạo ra trải nghiệm học tập thông minh và tương tác cho LingoAI.*
