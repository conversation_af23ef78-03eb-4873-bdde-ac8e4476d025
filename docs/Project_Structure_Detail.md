# 📂 LingoAI - C<PERSON><PERSON> Trúc Dự Án Chi Tiết

## 🏗️ Tổng Quan Cấu Trúc

LingoAI được tổ chức theo mô hình **Clean Architecture** với **MVVM pattern**, đảm bảo tách biệt rõ ràng giữa các layer và dễ dàng maintain.

## 📁 Cấu Trúc <PERSON>ư <PERSON>

```
LingoAI/
├── 📄 README.md                    # Tài liệu chính của dự án
├── 📄 build.gradle.kts            # Build configuration chính
├── 📄 settings.gradle.kts         # Project settings
├── 📄 gradle.properties           # Gradle properties
├── 📁 gradle/                     # Gradle wrapper và dependencies
│   ├── 📄 libs.versions.toml      # Version catalog
│   └── 📁 wrapper/                # Gradle wrapper files
├── 📁 app/                        # Main application module
└── 📁 docs/                       # Documentation (được tạo)
```

## 📱 App Module Structure

```
app/
├── 📄 build.gradle.kts            # App module build configuration
├── 📄 proguard-rules.pro          # ProGuard rules cho release build
└── 📁 src/
    ├── 📁 main/                   # Main source set
    ├── 📁 test/                   # Unit tests
    └── 📁 androidTest/            # Instrumentation tests
```

## 🎯 Main Source Structure

```
app/src/main/
├── 📄 AndroidManifest.xml         # App manifest với permissions
├── 📁 java/com/example/myapplication/  # Main source code
├── 📁 res/                        # Android resources
└── 📁 assets/                     # Static assets
    └── 📄 english_words.txt       # Word database cho suggestions
```

## 🔧 Source Code Organization

### 📦 Package Structure

```
com.example.myapplication/
├── 📄 MainActivity.kt             # Main entry point
├── 📄 SplashActivity.kt          # Splash screen activity
├── 📄 UserPreferences.kt         # User data management
├── 📁 api/                       # Network layer
├── 📁 navigation/                # Navigation & routing
└── 📁 ui/                        # UI layer (Jetpack Compose)
```

### 🌐 API Layer (`api/`)

```
api/
└── 📄 ApiService.kt              # Centralized API service
```

**Chức năng:**
- **Singleton Pattern**: Quản lý tất cả API calls
- **Dynamic Base URL**: Fetch URL từ Firebase
- **Authentication**: User ID based authentication
- **Error Handling**: Comprehensive error handling
- **HTTP Client**: OkHttp3 implementation

**Key Methods:**
- `fetchAndSetBaseUrl()`: Lấy base URL từ Firebase
- `loginUser()`, `registerUser()`: Authentication
- `searchVocabulary()`: Tra cứu từ vựng
- `detectObject()`: Object detection
- `generateText()`: AI chat response
- `getVocabularyList()`: Lịch sử từ vựng

### 🧭 Navigation Layer (`navigation/`)

```
navigation/
├── 📄 NavGraph.kt                # Navigation graph definition
├── 📄 AuthViewModel.kt           # Authentication state management
└── 📄 Routes.kt                  # Route constants
```

**NavGraph.kt:**
- **Compose Navigation**: Sử dụng NavHost
- **Route Management**: Quản lý tất cả routes
- **Authentication Flow**: Login/Register flow
- **Main App Flow**: Feature screens navigation
- **Deep Linking**: Support cho deep links

**Routes Object:**
```kotlin
object Routes {
    // Auth routes
    const val SPLASH = "splash"
    const val LOGIN = "login"
    const val REGISTER = "register"
    
    // Main features
    const val HOME = "home"
    const val PROFILE = "profile"
    const val WORD_GENIE = "word_genie"
    const val CHAT_SMART_AI = "chat_smart_ai"
    const val VISIONARY_WORDS = "visionary_words"
    const val HISTORY = "history"
    const val FLASHCARD = "flashcard"
    
    // Sub-routes
    const val VOCAB_INFO = "vocab_info"
    const val FLASHCARD_DETAIL = "flashcard_detail"
    const val FLASHCARD_STUDY = "flashcard_study"
}
```

### 🎨 UI Layer (`ui/`)

#### 🔐 Authentication (`ui/auth/`)

```
auth/
├── 📄 LoginScreen.kt             # Login interface
├── 📄 RegisterScreen.kt          # Registration interface
└── 📄 ProfileScreen.kt           # User profile management
```

**Features:**
- **Form Validation**: Input validation với error states
- **API Integration**: Login/Register API calls
- **State Management**: Loading states và error handling
- **Navigation**: Auto-navigation sau successful auth
- **User Data**: Save/load user preferences

#### 💬 Chat AI (`ui/chat/`)

```
chat/
├── 📄 ChatSmartAiWelcomeScreen.kt    # Welcome/intro screen
├── 📄 ChatSmartAiChatScreen.kt       # Main chat interface
├── 📄 RecordingManager.kt            # Audio recording management
└── 📄 AudioManager.kt                # Audio playback management
```

**ChatSmartAiChatScreen.kt:**
- **Real-time Chat**: Live chat interface với AI
- **Speech-to-Text**: Voice input integration
- **Text-to-Speech**: Audio output cho AI responses
- **Message Management**: Chat history và state
- **Typing Indicator**: Loading animation cho AI responses

**RecordingManager.kt:**
- **Audio Recording**: Android MediaRecorder integration
- **Permission Handling**: Microphone permission management
- **File Management**: Temporary audio file handling
- **State Management**: Recording states (idle, recording, processing)

#### 🧩 Common Components (`ui/common/`)

```
common/
├── 📄 MainScaffold.kt            # Main layout wrapper
├── 📄 BottomNav.kt               # Bottom navigation bar
├── 📄 UserComponents.kt          # User-related UI components
├── 📄 TinyLessonComponents.kt    # Learning components
├── 📄 WordCamComponents.kt       # Camera-related components
├── 📄 KeyboardDismissWrapper.kt  # Keyboard handling
└── 📄 AudioScreenWrapper.kt      # Audio screen wrapper
```

**MainScaffold.kt:**
- **Layout Management**: Scaffold với bottom navigation
- **Route-based UI**: Conditional bottom bar display
- **Navigation Handling**: Bottom nav click handling
- **Padding Management**: Safe area và keyboard handling

**BottomNav.kt:**
- **5-Tab Navigation**: Word, Chat, Vision, History, Cards
- **Animation**: Scale và color animations
- **State Management**: Active tab highlighting
- **Icon System**: Filled/Outlined icons cho states

#### 🃏 Flashcard System (`ui/flashcard/`)

```
flashcard/
├── 📄 FlashcardScreen.kt         # Main flashcard management
├── 📄 FlashcardDetailScreen.kt   # Individual set details
├── 📄 FlashcardStudyScreen.kt    # Study mode interface
├── 📄 FlashcardViewModel.kt      # State management
├── 📄 FlashcardRepository.kt     # Data repository
├── 📄 FlashcardDataStore.kt      # Local data storage
├── 📄 FlashcardModels.kt         # Data models
├── 📄 QuizletImporter.kt         # Quizlet import functionality
├── 📄 AddFlashcardDialog.kt      # Add card dialog
└── 📄 ImportFromQuizletDialog.kt # Import dialog
```

**Data Models:**
```kotlin
data class Flashcard(
    val id: String,
    val front: String,      // Từ tiếng Anh
    val back: String,       // Nghĩa tiếng Việt
    val ipa: String,        // Phiên âm
    val setId: String,
    val createdAt: Long,
    var isLearned: Boolean
)

data class FlashcardSet(
    val id: String,
    val name: String,
    val description: String,
    val createdAt: Long,
    val flashcards: List<Flashcard>
)
```

**Repository Pattern:**
- **Data Abstraction**: Abstract data access
- **Local Storage**: DataStore integration
- **User Isolation**: Per-user data separation
- **CRUD Operations**: Complete flashcard management
- **Batch Operations**: Efficient bulk operations

#### 📖 History Management (`ui/history/`)

```
history/
└── 📄 History.kt                 # History screen implementation
```

**Features:**
- **Lazy Loading**: Pagination với increasing page size
- **Search & Filter**: Real-time search functionality
- **CRUD Operations**: Delete vocabulary, add to flashcard
- **API Integration**: Vocabulary list API
- **State Management**: Loading states và error handling

#### 🏠 Home Dashboard (`ui/home/<USER>

```
home/
└── 📄 HomeScreen.kt              # Main dashboard
```

**Features:**
- **Feature Navigation**: Quick access to all features
- **User Greeting**: Personalized welcome message
- **Progress Tracking**: Learning progress display
- **Quick Actions**: Shortcut buttons to main features

#### 🎨 Theme System (`ui/theme/`)

```
theme/
├── 📄 Theme.kt                   # Main theme configuration
├── 📄 Color.kt                   # Color definitions
└── 📄 Type.kt                    # Typography definitions
```

**Color System:**
```kotlin
// Main colors
val MainColor = Color(0xFFF2CEDF)
val ButtonPrimary = Color(0xFFD17878)
val ButtonSecondary = Color(0xFFD9D9D9)

// Text colors
val TextPrimary = Color.Black
val TextSecondary = Color.Gray

// Feature-specific colors
val ChatUserBubble = Color(0xFFE3F2FD)
val ChatBotBubble = Color(0xFFD1C4E9)
val RecordingActive = Color(0xFFE57373)
```

**Typography:**
```kotlin
val RobotoFontFamily = FontFamily(
    Font(R.font.roboto_regular, FontWeight.Normal),
    Font(R.font.roboto_bold, FontWeight.Bold)
)
```

#### 👁️ Visionary Words (`ui/visionaryword/`)

```
visionaryword/
├── 📄 VisionaryWordsScreen.kt    # Main coordinator screen
├── 📄 VisionaryCameraScreen.kt   # Camera interface
└── 📄 VisionaryResultScreen.kt   # Object detection results
```

**Flow:**
1. **Welcome Screen**: Hướng dẫn sử dụng
2. **Camera Screen**: Capture image với Camera2 API
3. **Result Screen**: Display detected objects với labels

#### 📚 Word Genie (`ui/wordgenie/`)

```
wordgenie/
├── 📄 WordGenieScreen.kt         # Main word lookup interface
└── 📄 VocabInfo.kt               # Vocabulary information display
```

**Features:**
- **Auto-suggestion**: Word suggestions từ assets
- **API Integration**: Vocabulary search API
- **Rich Display**: IPA, meaning, related words
- **Audio Playback**: Pronunciation audio
- **Save Options**: Add to history và flashcard

## 📱 Resources Structure

```
res/
├── 📁 drawable/                  # Images, icons, vector drawables
├── 📁 font/                      # Custom fonts (Roboto family)
├── 📁 values/                    # Colors, strings, themes
│   ├── 📄 colors.xml
│   ├── 📄 strings.xml
│   └── 📄 themes.xml
└── 📁 xml/                       # Backup rules, network config
```

## 🔧 Configuration Files

### 📄 AndroidManifest.xml
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 📄 build.gradle.kts (App Level)
**Key Dependencies:**
- Jetpack Compose BOM
- Navigation Compose
- Camera2 & CameraX
- DataStore Preferences
- OkHttp3
- Kotlinx Serialization

## 🎯 Design Patterns Used

### 🏗️ Architectural Patterns
1. **MVVM**: Model-View-ViewModel separation
2. **Repository Pattern**: Data access abstraction
3. **Singleton**: ApiService, RecordingManager
4. **Observer Pattern**: StateFlow, Compose State
5. **Factory Pattern**: ViewModel creation

### 🔄 Data Flow Patterns
1. **Unidirectional Data Flow**: UI → ViewModel → Repository → API
2. **State Hoisting**: Compose state management
3. **Event-driven**: User actions trigger state changes
4. **Reactive Programming**: Flow-based data streams

### 🎨 UI Patterns
1. **Composition over Inheritance**: Compose components
2. **Reusable Components**: Common UI elements
3. **Theme-based Design**: Consistent styling
4. **Responsive Layout**: Adaptive UI design

---

*Cấu trúc này đảm bảo code dễ maintain, test và scale cho future development.*
