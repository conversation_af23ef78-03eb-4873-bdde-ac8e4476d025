# 📚 LingoAI - Tài Li<PERSON>u Kiến Trúc và Luồng Xử Lý

## 🌟 Tổng Quan Ứng Dụng

LingoAI là một ứng dụng Android học tiếng Anh thông minh được xây dựng bằng Kotlin và Jetpack Compose, tích hợp các công nghệ AI tiên tiến để mang lại trải nghiệm học tập tương tác và hiệu quả.

### 🎯 Mục Tiêu Chính
- Học từ vựng tiếng Anh thông minh với AI
- Luyện tập giao tiếp qua chat AI
- Nhận diện vật thể qua camera để học từ vựng
- Quản lý flashcard và lịch sử học tập

## 🏗️ Kiến Trúc Tổng Thể

### 📱 Architecture Pattern
- **MVVM (Model-View-ViewModel)** với Repository Pattern
- **Jetpack Compose** cho UI declarative
- **StateFlow** và Compose State cho state management
- **Navigation Component** cho điều hướng

### 🔧 Tech Stack
- **Language**: Kotlin 100%
- **UI Framework**: Jetpack Compose + Material Design 3
- **Architecture**: MVVM + Repository Pattern
- **State Management**: StateFlow, Compose State
- **Navigation**: Compose Navigation
- **Data Storage**: DataStore Preferences
- **HTTP Client**: OkHttp3
- **Image Processing**: Camera2 API
- **Audio**: MediaPlayer, Speech Recognition

## 📂 Cấu Trúc Dự Án

```
app/src/main/java/com/example/myapplication/
├── 📁 api/                    # API Services & Network Layer
│   └── ApiService.kt          # Centralized API service
├── 📁 navigation/             # Navigation & ViewModels
│   ├── NavGraph.kt           # Navigation graph definition
│   ├── AuthViewModel.kt      # Authentication state management
│   └── Routes.kt             # Route constants
├── 📁 ui/                    # UI Layer (Jetpack Compose)
│   ├── 📁 auth/              # Authentication screens
│   │   ├── LoginScreen.kt
│   │   ├── RegisterScreen.kt
│   │   └── ProfileScreen.kt
│   ├── 📁 chat/              # ChatSmart AI feature
│   │   ├── ChatSmartAiWelcomeScreen.kt
│   │   ├── ChatSmartAiChatScreen.kt
│   │   ├── RecordingManager.kt
│   │   └── AudioManager.kt
│   ├── 📁 common/            # Shared UI components
│   │   ├── MainScaffold.kt
│   │   ├── BottomNav.kt
│   │   ├── UserComponents.kt
│   │   └── TinyLessonComponents.kt
│   ├── 📁 flashcard/         # Flashcard management
│   │   ├── FlashcardScreen.kt
│   │   ├── FlashcardDetailScreen.kt
│   │   ├── FlashcardStudyScreen.kt
│   │   ├── FlashcardViewModel.kt
│   │   ├── FlashcardRepository.kt
│   │   ├── FlashcardDataStore.kt
│   │   ├── FlashcardModels.kt
│   │   └── QuizletImporter.kt
│   ├── 📁 history/           # History screen
│   │   └── History.kt
│   ├── 📁 home/              # Home dashboard
│   │   └── HomeScreen.kt
│   ├── 📁 theme/             # App theming
│   │   ├── Theme.kt
│   │   ├── Color.kt
│   │   └── Type.kt
│   ├── 📁 visionaryword/     # Camera & object detection
│   │   ├── VisionaryWordsScreen.kt
│   │   ├── VisionaryCameraScreen.kt
│   │   └── VisionaryResultScreen.kt
│   └── 📁 wordgenie/         # Word lookup feature
│       ├── WordGenieScreen.kt
│       └── VocabInfo.kt
├── MainActivity.kt           # Main activity & entry point
├── SplashActivity.kt        # Splash screen
└── UserPreferences.kt       # User data management
```

## 🔄 Luồng Xử Lý Chính

### 1. 🚀 Application Startup Flow
```
SplashActivity → MainActivity → Authentication Check → Main App
```

**Chi tiết:**
1. **SplashActivity**: Hiển thị logo và khởi tạo app
2. **MainActivity**: 
   - Kiểm tra permissions (Camera, Microphone, Storage)
   - Fetch base URL từ Firebase
   - Kiểm tra trạng thái đăng nhập từ UserPreferences
   - Khởi tạo ApiService với userId
3. **Navigation**: Điều hướng đến Login hoặc Home dựa trên trạng thái

### 2. 🔐 Authentication Flow
```
Login/Register → API Validation → Save UserPreferences → Navigate to Home
```

**Chi tiết:**
- **Input Validation**: Kiểm tra username/password format
- **API Call**: Gọi `/users/login` hoặc `/users/register`
- **Response Handling**: Lưu userId và username vào DataStore
- **State Update**: Cập nhật authentication state
- **Navigation**: Chuyển đến HomeScreen

### 3. 🏠 Main Navigation Flow
```
HomeScreen ↔ BottomNavigation ↔ Feature Screens
```

**Bottom Navigation có 5 tabs:**
- **Word Genie**: Tra cứu từ vựng
- **Chat**: ChatSmart AI
- **Vision**: Visionary Words (Camera)
- **History**: Lịch sử từ vựng
- **Cards**: Flashcard management

## 🎯 Tính Năng Chính và Luồng Xử Lý

### 📚 Word Genie (Tra cứu từ vựng)
**Luồng xử lý:**
1. User nhập từ tiếng Anh
2. Auto-suggestion từ `english_words.txt`
3. API call `/vocabulary/search`
4. Hiển thị kết quả: nghĩa, IPA, audio, từ liên quan
5. Option lưu vào lịch sử và flashcard

### 💬 ChatSmart AI
**Luồng xử lý:**
1. **Welcome Screen**: Giới thiệu tính năng
2. **Chat Screen**: 
   - Speech-to-Text: Record audio → API `/speech-to-text`
   - Send message → API `/generate-text`
   - Text-to-Speech: Play AI response
   - Real-time chat interface với typing indicator

### 👁️ Visionary Words (Camera AI)
**Luồng xử lý:**
1. **Welcome Screen**: Hướng dẫn sử dụng
2. **Camera Screen**: 
   - Capture image với Camera2 API
   - Image preprocessing
3. **Result Screen**:
   - Upload image → API `/detect-object`
   - Object detection với bounding boxes
   - Hiển thị labels tiếng Anh + nghĩa tiếng Việt
   - Option lưu từ vào vocabulary

### 📖 History (Lịch sử)
**Luồng xử lý:**
1. Load vocabulary list từ API `/vocabulary/list`
2. Lazy loading với pagination (pageSize tăng dần)
3. Display với search và filter
4. Actions: Delete, Add to Flashcard

### 🃏 Flashcard Management
**Luồng xử lý:**
1. **FlashcardScreen**: Quản lý các bộ thẻ
2. **Create/Edit Sets**: Tạo bộ thẻ mới
3. **Import từ Quizlet**: Parse URL và import cards
4. **Study Mode**: Flip animation, progress tracking
5. **Data Storage**: Local storage với DataStore

## 📊 Data Flow Architecture

### 🔄 State Management Pattern
```
UI Layer (Compose) ↔ ViewModel ↔ Repository ↔ Data Sources
                                    ↓
                            [API Service, DataStore]
```

### 📱 Data Sources
1. **Remote**: REST APIs qua ApiService
2. **Local**: DataStore Preferences cho user data và flashcards
3. **Assets**: `english_words.txt` cho word suggestions

### 🔄 Repository Pattern
- **FlashcardRepository**: Quản lý flashcard data
- **UserPreferences**: Quản lý user session
- **ApiService**: Centralized API calls

## 🎨 UI/UX Architecture

### 🎨 Theme System
- **Material Design 3** với custom color scheme
- **Typography**: Roboto font family
- **Colors**: Consistent color palette với MainColor, ButtonPrimary, etc.
- **Dark/Light Theme**: Dynamic color support

### 🧩 Component Architecture
- **MainScaffold**: Layout wrapper với BottomNavigation
- **Reusable Components**: UserComponents, TinyLessonComponents
- **Screen-specific Components**: Mỗi feature có components riêng

### 📱 Responsive Design
- **Adaptive Layout**: Hỗ trợ các kích thước màn hình
- **Animation**: Smooth transitions và micro-interactions
- **Accessibility**: Material Design accessibility guidelines

## 🔧 Technical Implementation Details

### 🌐 API Integration
- **Dynamic Base URL**: Fetch từ Firebase real-time
- **Error Handling**: Comprehensive error handling với user feedback
- **Authentication**: User ID based authentication
- **Retry Logic**: Automatic retry cho failed requests

### 💾 Data Persistence
- **UserPreferences**: DataStore cho user session
- **FlashcardDataStore**: JSON serialization cho flashcard data
- **Per-user Data**: Isolated data cho mỗi user

### 🎤 AI Features Integration
- **Speech Recognition**: Android SpeechRecognizer API
- **Text-to-Speech**: Android TTS engine
- **Object Detection**: Custom API với image upload
- **Natural Language**: AI chat API integration

## 🔒 Security & Privacy

### 🛡️ Data Security
- **Local Storage**: Encrypted DataStore preferences
- **API Communication**: HTTPS only
- **User Data**: Minimal data collection
- **Session Management**: Secure token handling

### 🔐 Permissions
- **Camera**: Cho Visionary Words feature
- **Microphone**: Cho ChatSmart AI speech input
- **Storage**: Cho image processing
- **Internet**: Bắt buộc cho tất cả API calls

## 📈 Performance Optimization

### ⚡ UI Performance
- **Lazy Loading**: LazyColumn cho large lists
- **Image Optimization**: Efficient image loading và caching
- **Compose Optimization**: Minimal recomposition
- **Memory Management**: Proper lifecycle handling

### 🌐 Network Optimization
- **Request Batching**: Batch API calls khi có thể
- **Caching Strategy**: Smart caching cho frequently accessed data
- **Offline Handling**: Graceful degradation khi offline

## 🚀 Deployment & Build

### 📦 Build Configuration
- **Gradle**: Kotlin DSL
- **ProGuard**: Code obfuscation cho release
- **Signing**: Release signing configuration
- **Flavors**: Debug/Release build variants

### 🔄 CI/CD Pipeline
- **Version Control**: Git với feature branches
- **Build Automation**: Gradle build scripts
- **Testing**: Unit tests và UI tests
- **Distribution**: APK generation và distribution

---

*Tài liệu này cung cấp cái nhìn tổng quan về kiến trúc LingoAI. Các phần chi tiết sẽ được mở rộng trong các tài liệu chuyên biệt.*
